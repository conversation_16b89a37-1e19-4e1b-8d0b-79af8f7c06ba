#!/usr/bin/env python3
"""
批量数据获取脚本
使用配置文件批量获取多个时间段和股票的数据

使用方法:
python scripts/batch_fetch_data.py --config scripts/data_fetch_config.yaml
"""

import os
import sys
import yaml
import argparse
import subprocess
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any
import time

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class BatchDataFetcher:
    """批量数据获取器"""
    
    def __init__(self, config_file: str):
        """
        初始化批量获取器
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = Path(config_file)
        self.config = self._load_config()
        self.project_root = project_root
        
        print(f"📋 配置文件: {self.config_file}")
        print(f"📁 项目根目录: {self.project_root}")
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            print("✅ 配置文件加载成功")
            return config
        except Exception as e:
            print(f"❌ 配置文件加载失败: {e}")
            raise
    
    def _validate_config(self) -> bool:
        """验证配置文件"""
        required_keys = ['symbols', 'date_ranges', 'analysts']
        
        for key in required_keys:
            if key not in self.config:
                print(f"❌ 配置文件缺少必需字段: {key}")
                return False
        
        # 验证股票代码
        if not self.config['symbols']:
            print("❌ 股票代码列表为空")
            return False
        
        # 验证时间段
        if not self.config['date_ranges']:
            print("❌ 时间段配置为空")
            return False
        
        # 验证分析师配置
        if not self.config['analysts'].get('enabled'):
            print("❌ 未启用任何分析师")
            return False
        
        print("✅ 配置验证通过")
        return True
    
    def _get_fetch_script_path(self) -> Path:
        """获取数据获取脚本路径"""
        script_path = self.project_root / "scripts" / "fetch_online_data.py"
        if not script_path.exists():
            raise FileNotFoundError(f"数据获取脚本不存在: {script_path}")
        return script_path
    
    def _build_command(self, symbols: List[str], start_date: str, end_date: str, 
                      analysts: List[str], data_dir: str = None) -> List[str]:
        """构建命令行参数"""
        script_path = self._get_fetch_script_path()
        
        cmd = [
            sys.executable,
            str(script_path),
            "--symbols", ",".join(symbols),
            "--start-date", start_date,
            "--end-date", end_date,
            "--analysts", ",".join(analysts),
            "--delay", str(self.config.get('delay', 1.0))
        ]
        
        if data_dir:
            cmd.extend(["--data-dir", data_dir])
        
        if self.config.get('verbose', False):
            cmd.append("--verbose")
        
        return cmd
    
    def _execute_command(self, cmd: List[str], task_name: str) -> bool:
        """执行命令"""
        print(f"\n🚀 执行任务: {task_name}")
        print(f"📝 命令: {' '.join(cmd)}")
        
        try:
            start_time = time.time()
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=3600)
            end_time = time.time()
            
            duration = end_time - start_time
            
            if result.returncode == 0:
                print(f"✅ 任务完成: {task_name} (耗时: {duration:.1f}秒)")
                if self.config.get('verbose', False):
                    print("📤 输出:")
                    print(result.stdout)
                return True
            else:
                print(f"❌ 任务失败: {task_name}")
                print("📤 错误输出:")
                print(result.stderr)
                return False
                
        except subprocess.TimeoutExpired:
            print(f"⏰ 任务超时: {task_name}")
            return False
        except Exception as e:
            print(f"❌ 执行异常: {e}")
            return False
    
    def run_batch_fetch(self) -> Dict[str, Any]:
        """执行批量数据获取"""
        if not self._validate_config():
            return {'success': False, 'error': '配置验证失败'}
        
        symbols = self.config['symbols']
        date_ranges = self.config['date_ranges']
        enabled_analysts = self.config['analysts']['enabled']
        data_dir = self.config.get('data_dir')
        
        print(f"\n📊 批量获取配置:")
        print(f"   股票数量: {len(symbols)}")
        print(f"   时间段数量: {len(date_ranges)}")
        print(f"   分析师类型: {', '.join(enabled_analysts)}")
        print(f"   总任务数: {len(date_ranges)}")
        
        results = {
            'start_time': datetime.now().isoformat(),
            'tasks': [],
            'summary': {
                'total': len(date_ranges),
                'success': 0,
                'failed': 0
            }
        }
        
        # 执行每个时间段的数据获取
        for i, date_range in enumerate(date_ranges, 1):
            task_name = f"{date_range.get('name', f'Task_{i}')} ({date_range['start_date']} ~ {date_range['end_date']})"
            
            # 构建命令
            cmd = self._build_command(
                symbols=symbols,
                start_date=date_range['start_date'],
                end_date=date_range['end_date'],
                analysts=enabled_analysts,
                data_dir=data_dir
            )
            
            # 执行命令
            success = self._execute_command(cmd, task_name)
            
            # 记录结果
            task_result = {
                'name': task_name,
                'start_date': date_range['start_date'],
                'end_date': date_range['end_date'],
                'symbols': symbols,
                'analysts': enabled_analysts,
                'success': success,
                'timestamp': datetime.now().isoformat()
            }
            
            results['tasks'].append(task_result)
            
            if success:
                results['summary']['success'] += 1
            else:
                results['summary']['failed'] += 1
            
            # 任务间延迟
            if i < len(date_ranges):
                delay = self.config.get('delay', 1.0) * 2  # 任务间延迟稍长
                print(f"⏳ 等待 {delay} 秒后执行下一个任务...")
                time.sleep(delay)
        
        results['end_time'] = datetime.now().isoformat()
        
        # 保存批量执行结果
        self._save_batch_results(results)
        
        # 打印汇总
        self._print_summary(results)
        
        return results
    
    def _save_batch_results(self, results: Dict[str, Any]):
        """保存批量执行结果"""
        try:
            results_dir = self.project_root / "logs"
            results_dir.mkdir(exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            results_file = results_dir / f"batch_fetch_results_{timestamp}.json"
            
            import json
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            
            print(f"📄 批量执行结果已保存: {results_file}")
            
        except Exception as e:
            print(f"⚠️  保存批量执行结果失败: {e}")
    
    def _print_summary(self, results: Dict[str, Any]):
        """打印执行汇总"""
        summary = results['summary']
        
        print(f"\n📈 批量执行汇总:")
        print(f"   总任务数: {summary['total']}")
        print(f"   成功任务: {summary['success']}")
        print(f"   失败任务: {summary['failed']}")
        print(f"   成功率: {summary['success']/summary['total']*100:.1f}%")
        
        if summary['failed'] > 0:
            print(f"\n❌ 失败的任务:")
            for task in results['tasks']:
                if not task['success']:
                    print(f"   - {task['name']}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='批量获取TradingAgents在线数据',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 使用默认配置文件
  python scripts/batch_fetch_data.py
  
  # 使用自定义配置文件
  python scripts/batch_fetch_data.py --config my_config.yaml
  
  # 显示详细输出
  python scripts/batch_fetch_data.py --config my_config.yaml --verbose

配置文件格式:
  参考 scripts/data_fetch_config.yaml 示例文件
        """
    )
    
    parser.add_argument('--config', 
                       default='scripts/data_fetch_config.yaml',
                       help='配置文件路径 (默认: scripts/data_fetch_config.yaml)')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='显示详细输出')
    
    args = parser.parse_args()
    
    # 检查配置文件是否存在
    config_path = Path(args.config)
    if not config_path.exists():
        print(f"❌ 配置文件不存在: {config_path}")
        print("💡 请创建配置文件或使用 --config 参数指定正确的路径")
        return
    
    try:
        # 创建批量获取器并执行
        fetcher = BatchDataFetcher(args.config)
        
        if args.verbose:
            fetcher.config['verbose'] = True
        
        results = fetcher.run_batch_fetch()
        
        if results['summary']['failed'] == 0:
            print("\n🎉 所有任务执行成功!")
        else:
            print(f"\n⚠️  有 {results['summary']['failed']} 个任务执行失败")
            
    except KeyboardInterrupt:
        print("\n⏹️  用户中断操作")
    except Exception as e:
        print(f"\n❌ 执行失败: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()


if __name__ == '__main__':
    main()
