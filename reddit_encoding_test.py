#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Reddit编码测试脚本

测试编码修复是否有效，获取最近1天的数据进行验证
"""

import os
import sys
import io

# 修复Windows编码问题
if sys.platform == 'win32':
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    if hasattr(sys.stdout, 'buffer'):
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')
    if hasattr(sys.stderr, 'buffer'):
        sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8', errors='replace')

import praw
import json
import time
from datetime import datetime, timedelta
from pathlib import Path
import logging


class RedditEncodingTester:
    """Reddit编码测试器"""
    
    def __init__(self):
        """初始化测试器"""
        # 设置日志 - 安全编码
        self.setup_logging()
        
        # 初始化Reddit API
        self.setup_reddit_api()
        
        # 测试配置
        self.target_stocks = {
            'AAPL': {
                'keywords': ['AAPL', 'Apple'],
                'folder': 'AAPL_Reddit_Test'
            }
        }
        
        # 只测试今天
        self.test_date = datetime.now()
        
        # 测试subreddit
        self.subreddits = ['stocks', 'investing']
    
    def setup_logging(self):
        """设置日志系统 - 安全编码版本"""
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        
        # 文件处理器
        file_handler = logging.FileHandler('reddit_encoding_test.log', encoding='utf-8')
        file_handler.setFormatter(formatter)
        
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        
        # 配置日志器
        self.logger = logging.getLogger(__name__)
        self.logger.setLevel(logging.INFO)
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
    
    def setup_reddit_api(self):
        """设置Reddit API连接"""
        try:
            self.reddit = praw.Reddit(
                client_id="vyg0MLjDgpqYQRNHaJaJEQ",
                client_secret="hy412_1dYvB0xXKe5tUWYCTSeFZpGQ",
                user_agent="ai_fund_hedge by /u/Available_Neck_1936"
            )
            
            # 测试连接
            self.reddit.user.me()
            self.logger.info("[SUCCESS] ✅ Reddit API连接成功")  # 测试emoji
            
        except Exception as e:
            self.logger.error(f"[ERROR] ❌ Reddit API连接失败: {e}")  # 测试emoji
            raise
    
    def test_encoding_with_emojis(self):
        """测试各种特殊字符和emoji"""
        test_messages = [
            "[SUCCESS] ✅ 测试成功",
            "[ERROR] ❌ 测试错误", 
            "[WARNING] ⚠️ 测试警告",
            "[INFO] 📊 测试信息",
            "[FETCH] 🔍 测试获取",
            "[SAVE] 💾 测试保存",
            "[COMPLETE] 🎯 测试完成",
            "中文测试：股票、情绪、分析",
            "特殊符号：©®™€£¥§¶†‡•…‰‹›""''",
            "数学符号：±×÷≤≥≠∞∑∏∫√∂∆∇"
        ]
        
        self.logger.info("[TEST] 开始编码测试...")
        
        for i, message in enumerate(test_messages, 1):
            try:
                self.logger.info(f"[{i:02d}] {message}")
                time.sleep(0.1)  # 小延迟
            except Exception as e:
                self.logger.error(f"[ERROR] 消息 {i} 编码失败: {e}")
        
        self.logger.info("[TEST] 编码测试完成")
    
    def fetch_sample_data(self):
        """获取样本数据测试"""
        self.logger.info("[FETCH] 🔍 开始获取样本数据...")
        
        config = self.target_stocks['AAPL']
        folder_path = Path(config['folder'])
        folder_path.mkdir(exist_ok=True)
        
        all_posts = []
        
        for subreddit_name in self.subreddits:
            try:
                self.logger.info(f"[SUBREDDIT] 📂 处理 {subreddit_name}...")
                
                subreddit = self.reddit.subreddit(subreddit_name)
                
                for keyword in config['keywords']:
                    try:
                        self.logger.info(f"[SEARCH] 🔍 搜索关键词: {keyword}")
                        
                        search_results = subreddit.search(
                            keyword,
                            time_filter="week",
                            sort="relevance",
                            limit=3
                        )
                        
                        for post in search_results:
                            post_data = {
                                'id': post.id,
                                'title': post.title,
                                'content': post.selftext[:200],  # 限制长度
                                'score': post.score,
                                'num_comments': post.num_comments,
                                'subreddit': subreddit_name,
                                'created_date': datetime.fromtimestamp(post.created_utc).strftime("%Y-%m-%d %H:%M:%S")
                            }
                            
                            all_posts.append(post_data)
                            
                            self.logger.info(f"[POST] 📝 找到帖子: {post.title[:50]}...")
                        
                        time.sleep(1)
                        
                    except Exception as e:
                        self.logger.warning(f"[WARNING] ⚠️ 搜索失败: {e}")
                        continue
                
                time.sleep(2)
                
            except Exception as e:
                self.logger.warning(f"[WARNING] ⚠️ subreddit访问失败: {e}")
                continue
        
        # 保存测试数据
        if all_posts:
            self.save_test_data(all_posts)
            self.logger.info(f"[SUCCESS] ✅ 获取到 {len(all_posts)} 个帖子")
        else:
            self.logger.warning("[WARNING] ⚠️ 未获取到任何帖子")
    
    def save_test_data(self, posts):
        """保存测试数据"""
        config = self.target_stocks['AAPL']
        folder_path = Path(config['folder'])
        
        date_str = self.test_date.strftime("%Y-%m-%d")
        json_file = folder_path / f"test_{date_str}.json"
        txt_file = folder_path / f"test_{date_str}.txt"
        
        # 保存JSON
        test_data = {
            'test_date': date_str,
            'total_posts': len(posts),
            'posts': posts,
            'encoding_test': True,
            'special_chars': "✅❌⚠️📊🔍💾🎯",
            'chinese_text': "这是中文测试文本",
            'timestamp': datetime.now().isoformat()
        }
        
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(test_data, f, ensure_ascii=False, indent=2)
        
        # 保存文本
        with open(txt_file, 'w', encoding='utf-8') as f:
            f.write("Reddit编码测试结果\n")
            f.write("=" * 30 + "\n\n")
            f.write(f"测试日期: {date_str}\n")
            f.write(f"总帖子数: {len(posts)}\n")
            f.write(f"编码测试: ✅ 成功\n")
            f.write(f"特殊字符: ✅❌⚠️📊🔍💾🎯\n")
            f.write(f"中文测试: 这是中文测试文本\n\n")
            
            f.write("帖子详情:\n")
            f.write("-" * 20 + "\n")
            
            for i, post in enumerate(posts, 1):
                f.write(f"\n{i}. [{post['subreddit']}] {post['title']}\n")
                f.write(f"   得分: {post['score']} | 评论: {post['num_comments']}\n")
                f.write(f"   时间: {post['created_date']}\n")
                if post['content']:
                    f.write(f"   内容: {post['content']}\n")
        
        self.logger.info(f"[SAVE] 💾 测试数据已保存: {json_file}")
    
    def run_test(self):
        """运行编码测试"""
        self.logger.info("[START] 🚀 开始Reddit编码测试")
        
        try:
            # 1. 测试各种字符编码
            self.test_encoding_with_emojis()
            
            # 2. 测试实际数据获取
            self.fetch_sample_data()
            
            self.logger.info("[COMPLETE] 🎯 编码测试完成!")
            
        except Exception as e:
            self.logger.error(f"[ERROR] ❌ 测试失败: {e}")
            raise


def main():
    """主函数"""
    print("[START] 🚀 Reddit编码测试脚本")
    print("=" * 40)
    print("测试目标: 验证编码修复是否有效")
    print("测试内容: emoji、中文、特殊字符")
    print("=" * 40)
    
    try:
        tester = RedditEncodingTester()
        tester.run_test()
        
        print("\n[SUCCESS] ✅ 编码测试成功!")
        print("如果您看到这条消息且没有编码错误，说明修复有效。")
        
    except KeyboardInterrupt:
        print("\n[INTERRUPT] ⚠️ 用户中断测试")
    except Exception as e:
        print(f"\n[ERROR] ❌ 测试出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
