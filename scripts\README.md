# TradingAgents 在线数据获取工具

本目录包含用于获取TradingAgents系统四个分析师代理所需在线数据的脚本和配置文件。

## 📁 文件结构

```
scripts/
├── README.md                    # 本文件
├── fetch_online_data.py         # 单次数据获取脚本
├── batch_fetch_data.py          # 批量数据获取脚本
├── data_fetch_config.yaml       # 批量获取配置文件
└── test_data_fetch.py           # 测试脚本
```

## 🚀 快速开始

### 1. 环境准备

确保已配置必需的API密钥：

```bash
# 在项目根目录的 .env 文件中配置
DEEPSEEK_API_KEY=your_deepseek_api_key_here
FINNHUB_API_KEY=your_finnhub_api_key_here
```

### 2. 测试工具

运行测试脚本验证工具是否正常工作：

```bash
python scripts/test_data_fetch.py
```

### 3. 获取数据

#### 单次获取
```bash
# 获取单个股票一周数据
python scripts/fetch_online_data.py --symbols AAPL --start-date 2024-01-01 --end-date 2024-01-07

# 获取多个股票一个月数据
python scripts/fetch_online_data.py --symbols AAPL,MSFT,GOOGL --start-date 2024-01-01 --end-date 2024-01-31
```

#### 批量获取
```bash
# 使用配置文件批量获取
python scripts/batch_fetch_data.py --config scripts/data_fetch_config.yaml
```

## 📋 脚本详细说明

### 1. fetch_online_data.py - 单次数据获取脚本

**功能**: 获取指定股票和时间段的数据

**参数**:
- `--symbols`: 股票代码 (必需)
- `--start-date`: 开始日期 (必需)
- `--end-date`: 结束日期 (必需)
- `--analysts`: 分析师类型 (可选)
- `--data-dir`: 数据目录 (可选)
- `--delay`: API调用间隔 (可选)
- `--verbose`: 详细输出 (可选)

**示例**:
```bash
# 基本用法
python scripts/fetch_online_data.py --symbols AAPL --start-date 2024-01-01 --end-date 2024-01-31

# 高级用法
python scripts/fetch_online_data.py \
  --symbols AAPL,MSFT \
  --start-date 2024-01-01 \
  --end-date 2024-01-31 \
  --analysts market,news \
  --delay 2.0 \
  --verbose
```

### 2. batch_fetch_data.py - 批量数据获取脚本

**功能**: 使用配置文件批量获取多个时间段的数据

**参数**:
- `--config`: 配置文件路径 (可选，默认使用 data_fetch_config.yaml)
- `--verbose`: 详细输出 (可选)

**示例**:
```bash
# 使用默认配置
python scripts/batch_fetch_data.py

# 使用自定义配置
python scripts/batch_fetch_data.py --config my_config.yaml --verbose
```

### 3. data_fetch_config.yaml - 配置文件

**功能**: 定义批量获取的股票、时间段和分析师类型

**主要配置项**:
```yaml
symbols:          # 股票代码列表
  - "AAPL"
  - "MSFT"

date_ranges:      # 时间段配置
  - start_date: "2024-01-01"
    end_date: "2024-03-31"
    name: "2024_Q1"

analysts:         # 分析师配置
  enabled:
    - "market"
    - "news"
    - "social"
    - "fundamentals"
```

### 4. test_data_fetch.py - 测试脚本

**功能**: 测试数据获取工具是否正常工作

**测试项目**:
- 环境配置检查
- 脚本文件检查
- 配置文件验证
- 单次数据获取测试
- 数据输出检查

**示例**:
```bash
python scripts/test_data_fetch.py
```

## 📊 数据获取详情

### 支持的分析师类型

1. **market** - 市场分析师
   - Yahoo Finance价格数据
   - 技术指标 (RSI, MACD, 布林带等)

2. **news** - 新闻分析师
   - 全球宏观经济新闻
   - 股票相关新闻

3. **social** - 社交媒体分析师
   - 社交媒体情绪和讨论

4. **fundamentals** - 基本面分析师
   - 公司基本面信息
   - 财务指标

### 数据保存结构

```
data/
├── market_data/
│   ├── price_data/
│   └── indicators/
├── news_data/
│   ├── global/
│   └── google/
├── social_data/
└── fundamentals_data/
```

## ⚙️ 配置和优化

### API调用优化

1. **调用间隔**: 使用 `--delay` 参数控制API调用频率
2. **批量处理**: 使用批量脚本减少重复操作
3. **选择性获取**: 使用 `--analysts` 参数只获取需要的数据

### 成本控制建议

1. **测试阶段**: 使用短时间范围和少量股票
2. **生产环境**: 分批获取，监控API使用量
3. **数据复用**: 避免重复获取相同数据

## 🔧 故障排除

### 常见问题

1. **API密钥错误**
   ```
   ❌ 缺少以下API密钥: DEEPSEEK_API_KEY
   ```
   **解决**: 检查 `.env` 文件配置

2. **网络连接问题**
   ```
   ❌ 获取数据失败: Connection timeout
   ```
   **解决**: 增加 `--delay` 参数值

3. **日期格式错误**
   ```
   ❌ 日期格式错误，请使用 YYYY-MM-DD 格式
   ```
   **解决**: 确保日期格式正确

### 调试技巧

1. **使用测试脚本**: 先运行 `test_data_fetch.py` 检查环境
2. **详细输出**: 使用 `--verbose` 参数查看详细信息
3. **单步测试**: 先测试单个股票和短时间范围
4. **检查日志**: 查看生成的汇总文件了解执行情况

## 📚 相关文档

- [分析师数据来源说明](../docs/analysts_data_sources.md)
- [在线数据获取指南](../docs/online_data_fetching_guide.md)
- [项目主要文档](../README.md)

## 💡 使用建议

1. **首次使用**: 先运行测试脚本确保环境正确
2. **小规模测试**: 使用少量股票和短时间范围进行测试
3. **监控成本**: 定期检查API使用量和费用
4. **数据备份**: 重要数据及时备份
5. **定期更新**: 保持数据的时效性

## 🆘 获取帮助

如果遇到问题，可以：

1. 查看脚本的帮助信息：
   ```bash
   python scripts/fetch_online_data.py --help
   python scripts/batch_fetch_data.py --help
   ```

2. 运行测试脚本诊断问题：
   ```bash
   python scripts/test_data_fetch.py
   ```

3. 检查相关文档和配置文件

---

*最后更新: 2025-01-25*
