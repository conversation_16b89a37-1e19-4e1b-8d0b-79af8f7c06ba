#!/usr/bin/env python3
"""
Reddit批量获取测试脚本

测试版本：获取AAPL、MSFT、NVDA三只股票最近3天的数据
用于验证脚本功能是否正常工作

使用方法：
python reddit_batch_test.py
"""

import praw
import json
import time
import os
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any
import logging
from collections import defaultdict


class RedditBatchTester:
    """Reddit批量获取测试器"""
    
    def __init__(self):
        """初始化测试器"""
        # 设置日志 - 修复编码问题
        import sys

        # 设置控制台输出编码为UTF-8
        if sys.stdout.encoding != 'utf-8':
            import io
            sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')

        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('reddit_batch_test.log', encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        # 初始化Reddit API
        self.reddit = praw.Reddit(
            client_id="vyg0MLjDgpqYQRNHaJaJEQ",
            client_secret="hy412_1dYvB0xXKe5tUWYCTSeFZpGQ",
            user_agent="ai_fund_hedge by /u/Available_Neck_1936"
        )
        
        # 测试配置 - 只获取最近3天的数据
        self.target_stocks = {
            'AAPL': {
                'name': 'Apple',
                'keywords': ['AAPL', 'Apple'],
                'folder': 'AAPL_Reddit'
            },
            'MSFT': {
                'name': 'Microsoft', 
                'keywords': ['MSFT', 'Microsoft'],
                'folder': 'MSFT_Reddit'
            },
            'NVDA': {
                'name': 'Nvidia',
                'keywords': ['NVDA', 'Nvidia'],
                'folder': 'NVDA_Reddit'
            }
        }
        
        # 测试时间范围 - 最近3天
        self.end_date = datetime.now()
        self.start_date = self.end_date - timedelta(days=2)  # 3天数据
        
        # 测试subreddit - 减少数量加快测试
        self.subreddits = ['stocks', 'investing', 'wallstreetbets']
        
        # 测试目标 - 每日至少5条帖子
        self.min_posts_per_day = 5
        
        # 情绪关键词
        self.sentiment_keywords = {
            'positive': ['bullish', 'buy', 'good', 'great', 'up', 'rise', 'gain', 'profit'],
            'negative': ['bearish', 'sell', 'bad', 'down', 'fall', 'loss', 'crash']
        }
    
    def calculate_sentiment_score(self, text: str) -> Dict[str, Any]:
        """计算情绪得分"""
        text_lower = text.lower()
        
        positive_count = sum(1 for keyword in self.sentiment_keywords['positive'] 
                           if keyword in text_lower)
        negative_count = sum(1 for keyword in self.sentiment_keywords['negative'] 
                           if keyword in text_lower)
        
        total_keywords = positive_count + negative_count
        if total_keywords == 0:
            sentiment_score = 0.0
            sentiment_label = 'neutral'
        else:
            sentiment_score = (positive_count - negative_count) / total_keywords
            if sentiment_score > 0.2:
                sentiment_label = 'positive'
            elif sentiment_score < -0.2:
                sentiment_label = 'negative'
            else:
                sentiment_label = 'neutral'
        
        return {
            'score': round(sentiment_score, 3),
            'label': sentiment_label,
            'positive_keywords': positive_count,
            'negative_keywords': negative_count
        }
    
    def is_relevant_post(self, post, keywords: List[str]) -> bool:
        """检查帖子相关性"""
        title = post.title.lower()
        content = post.selftext.lower()
        
        for keyword in keywords:
            if keyword.lower() in title or keyword.lower() in content:
                return True
        return False
    
    def fetch_posts_for_date(self, ticker: str, target_date: datetime) -> List[Dict]:
        """获取指定日期的帖子"""
        config = self.target_stocks[ticker]
        keywords = config['keywords']
        
        self.logger.info(f"   🔍 获取 {ticker} 在 {target_date.strftime('%Y-%m-%d')} 的帖子...")
        
        all_posts = []
        
        for subreddit_name in self.subreddits:
            try:
                subreddit = self.reddit.subreddit(subreddit_name)
                
                for keyword in keywords:
                    try:
                        search_results = subreddit.search(
                            keyword,
                            time_filter="week",
                            sort="relevance",
                            limit=10
                        )
                        
                        for post in search_results:
                            # 检查帖子日期
                            post_date = datetime.fromtimestamp(post.created_utc)
                            if abs((post_date.date() - target_date.date()).days) > 1:
                                continue
                            
                            # 检查相关性
                            if not self.is_relevant_post(post, keywords):
                                continue
                            
                            # 计算情绪得分
                            full_text = f"{post.title} {post.selftext}"
                            sentiment = self.calculate_sentiment_score(full_text)
                            
                            # 构建帖子数据
                            post_data = {
                                'id': post.id,
                                'title': post.title,
                                'content': post.selftext,
                                'score': post.score,
                                'num_comments': post.num_comments,
                                'created_date': post_date.strftime("%Y-%m-%d %H:%M:%S"),
                                'author': str(post.author) if post.author else '[deleted]',
                                'subreddit': subreddit_name,
                                'sentiment': sentiment,
                                'permalink': f"https://reddit.com{post.permalink}"
                            }
                            
                            all_posts.append(post_data)
                        
                        time.sleep(1)  # API限制
                        
                    except Exception as e:
                        self.logger.warning(f"     搜索 {keyword} 失败: {e}")
                        continue
                
                time.sleep(2)  # subreddit间延迟
                
            except Exception as e:
                self.logger.warning(f"     访问 {subreddit_name} 失败: {e}")
                continue
        
        # 去重
        seen_ids = set()
        unique_posts = []
        for post in all_posts:
            if post['id'] not in seen_ids:
                seen_ids.add(post['id'])
                unique_posts.append(post)
        
        # 按分数排序
        unique_posts.sort(key=lambda x: x['score'], reverse=True)
        
        self.logger.info(f"     ✅ 找到 {len(unique_posts)} 个相关帖子")
        return unique_posts
    
    def save_daily_posts(self, ticker: str, date: datetime, posts: List[Dict]):
        """保存每日帖子数据"""
        config = self.target_stocks[ticker]
        folder_path = Path(config['folder'])
        folder_path.mkdir(exist_ok=True)
        
        date_str = date.strftime("%Y-%m-%d")
        json_file = folder_path / f"{date_str}.json"
        txt_file = folder_path / f"{date_str}.txt"
        
        # 统计信息
        total_posts = len(posts)
        sentiment_stats = {
            'positive': len([p for p in posts if p['sentiment']['label'] == 'positive']),
            'negative': len([p for p in posts if p['sentiment']['label'] == 'negative']),
            'neutral': len([p for p in posts if p['sentiment']['label'] == 'neutral'])
        }
        
        avg_sentiment = sum(p['sentiment']['score'] for p in posts) / len(posts) if posts else 0
        
        save_data = {
            'ticker': ticker,
            'date': date_str,
            'total_posts': total_posts,
            'posts': posts,
            'statistics': {
                'sentiment_distribution': sentiment_stats,
                'average_sentiment_score': round(avg_sentiment, 3)
            },
            'metadata': {
                'fetch_time': datetime.now().isoformat(),
                'target_met': total_posts >= self.min_posts_per_day
            }
        }
        
        # 保存JSON
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(save_data, f, ensure_ascii=False, indent=2)
        
        # 保存文本
        with open(txt_file, 'w', encoding='utf-8') as f:
            f.write(f"{ticker} Reddit帖子数据 - {date_str}\n")
            f.write("=" * 40 + "\n\n")
            f.write(f"总帖子数: {total_posts}\n")
            f.write(f"目标达成: {'✅' if total_posts >= self.min_posts_per_day else '❌'}\n")
            f.write(f"平均情绪得分: {avg_sentiment:.3f}\n\n")
            
            f.write("情绪分布:\n")
            for sentiment, count in sentiment_stats.items():
                f.write(f"  {sentiment}: {count}\n")
            f.write("\n")
            
            f.write("帖子详情:\n")
            f.write("-" * 30 + "\n")
            for i, post in enumerate(posts, 1):
                f.write(f"\n{i}. 【{post['subreddit']}】{post['title']}\n")
                f.write(f"   得分: {post['score']} | 评论: {post['num_comments']}\n")
                f.write(f"   情绪: {post['sentiment']['label']} ({post['sentiment']['score']})\n")
                f.write(f"   时间: {post['created_date']}\n")
                if post['content']:
                    content_preview = post['content'][:100].replace('\n', ' ')
                    if len(post['content']) > 100:
                        content_preview += "..."
                    f.write(f"   内容: {content_preview}\n")
        
        self.logger.info(f"     💾 保存到: {json_file}")
        return save_data
    
    def run_test(self):
        """运行测试"""
        self.logger.info("🧪 开始Reddit批量获取测试")
        self.logger.info(f"📅 测试时间范围: {self.start_date.strftime('%Y-%m-%d')} 到 {self.end_date.strftime('%Y-%m-%d')}")
        self.logger.info(f"📊 测试股票: {list(self.target_stocks.keys())}")
        
        # 生成日期列表
        dates = []
        current_date = self.start_date
        while current_date <= self.end_date:
            dates.append(current_date)
            current_date += timedelta(days=1)
        
        results = {}
        
        # 处理每个股票
        for ticker in self.target_stocks.keys():
            self.logger.info(f"\n🚀 测试 {ticker}...")
            
            stock_results = []
            
            for date in dates:
                try:
                    posts = self.fetch_posts_for_date(ticker, date)
                    save_data = self.save_daily_posts(ticker, date, posts)
                    stock_results.append(save_data)
                    
                    time.sleep(3)  # 延迟
                    
                except Exception as e:
                    self.logger.error(f"   ❌ {date.strftime('%Y-%m-%d')} 处理失败: {e}")
                    continue
            
            results[ticker] = stock_results
        
        # 生成测试报告
        self.generate_test_report(results)
        
        self.logger.info("\n🎯 测试完成!")
    
    def generate_test_report(self, results: Dict):
        """生成测试报告"""
        report_file = Path("reddit_batch_test_report.txt")
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("Reddit批量获取测试报告\n")
            f.write("=" * 40 + "\n\n")
            f.write(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"测试范围: {self.start_date.strftime('%Y-%m-%d')} 到 {self.end_date.strftime('%Y-%m-%d')}\n\n")
            
            for ticker, stock_data in results.items():
                f.write(f"{ticker} 测试结果:\n")
                f.write("-" * 20 + "\n")
                
                total_posts = sum(data['total_posts'] for data in stock_data)
                successful_days = sum(1 for data in stock_data if data['metadata']['target_met'])
                
                f.write(f"  总帖子数: {total_posts}\n")
                f.write(f"  成功天数: {successful_days}/{len(stock_data)}\n")
                f.write(f"  成功率: {successful_days/len(stock_data)*100:.1f}%\n")
                
                if stock_data:
                    avg_sentiment = sum(data['statistics']['average_sentiment_score'] for data in stock_data) / len(stock_data)
                    f.write(f"  平均情绪: {avg_sentiment:.3f}\n")
                
                f.write("\n")
        
        self.logger.info(f"📄 测试报告已保存: {report_file}")


def main():
    """主函数"""
    print("🧪 Reddit批量获取测试脚本")
    print("=" * 40)
    print("测试股票: AAPL, MSFT, NVDA")
    print("测试范围: 最近3天")
    print("测试目标: 每日至少5条帖子")
    print("=" * 40)
    
    try:
        tester = RedditBatchTester()
        tester.run_test()
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
