# 社交媒体分析师数据获取指南

## 概述

社交媒体分析师是TradingAgents系统中负责收集和分析股票相关社交媒体内容的组件。本指南详细介绍了社交媒体数据的获取过程、数据来源和使用方法。

## 数据来源

### 1. 在线模式 (Online Mode)

**数据源**: DeepSeek/OpenAI API
- **优势**: 实时数据，内容丰富，覆盖面广
- **劣势**: 需要API密钥，有使用成本，依赖网络
- **适用场景**: 需要最新社交媒体数据的实时分析

**获取过程**:
1. 通过LLM API发送查询请求
2. 搜索指定股票在过去7天的社交媒体内容
3. 包括Twitter/X、Reddit、财经论坛等平台讨论
4. 返回结构化的分析结果

**支持的平台**:
- Twitter/X 讨论
- Reddit 股票板块
- 财经社交媒体平台
- 投资者论坛

### 2. 离线模式 (Offline Mode)

**数据源**: 预处理的Reddit数据
- **优势**: 无需API密钥，响应快速，成本低
- **劣势**: 数据有限，需要预先下载，更新不及时
- **适用场景**: 历史数据分析，批量处理，开发测试

**数据存储结构**:
```
data/
└── reddit_data/
    └── company_news/
        ├── stocks/
        │   └── 2024-05-20.jsonl
        ├── investing/
        │   └── 2024-05-20.jsonl
        └── wallstreetbets/
            └── 2024-05-20.jsonl
```

**获取过程**:
1. 扫描本地Reddit数据文件
2. 根据股票代码和公司名称过滤相关帖子
3. 按时间范围筛选数据
4. 按热度排序返回结果

## 独立数据获取脚本

### 脚本位置
`scripts/social_media_data_fetcher.py`

### 主要功能

1. **环境检查**: 自动检测API密钥和数据目录
2. **双模式支持**: 支持在线和离线两种获取模式
3. **质量评估**: 自动评估获取内容的质量
4. **结果保存**: 支持JSON和文本格式保存
5. **详细日志**: 提供详细的获取过程信息

### 使用方法

#### 基本用法

```bash
# 在线模式获取AAPL数据
python scripts/social_media_data_fetcher.py --ticker AAPL --date 2024-05-20 --mode online

# 离线模式获取TSLA数据  
python scripts/social_media_data_fetcher.py --ticker TSLA --date 2024-05-20 --mode offline

# 同时使用两种模式
python scripts/social_media_data_fetcher.py --ticker NVDA --date 2024-05-20 --mode both
```

#### 高级选项

```bash
# 保存结果到文件
python scripts/social_media_data_fetcher.py --ticker AAPL --date 2024-05-20 --save

# 指定输出目录
python scripts/social_media_data_fetcher.py --ticker AAPL --date 2024-05-20 --output ./my_results

# 静默模式（减少输出）
python scripts/social_media_data_fetcher.py --ticker AAPL --date 2024-05-20 --quiet
```

### 参数说明

| 参数 | 简写 | 必需 | 说明 |
|------|------|------|------|
| `--ticker` | `-t` | ✅ | 股票代码 (如: AAPL, TSLA) |
| `--date` | `-d` | ✅ | 目标日期 (YYYY-MM-DD) |
| `--mode` | `-m` | ❌ | 获取模式: online/offline/both |
| `--output` | `-o` | ❌ | 输出目录路径 |
| `--save` | | ❌ | 保存结果到文件 |
| `--quiet` | `-q` | ❌ | 静默模式 |

## 数据获取过程详解

### 在线模式流程

1. **环境检查**
   - 检测DEEPSEEK_API_KEY或OPENAI_API_KEY
   - 初始化LLM客户端
   - 确定使用的模型

2. **构建查询**
   - 生成针对特定股票的查询提示
   - 指定时间范围（目标日期前7天）
   - 明确需要的内容类型

3. **API调用**
   - 发送请求到DeepSeek/OpenAI API
   - 设置合适的温度和token限制
   - 记录响应时间和token使用量

4. **结果处理**
   - 提取响应内容
   - 评估内容质量
   - 格式化返回结果

### 离线模式流程

1. **数据目录检查**
   - 验证Reddit数据目录存在
   - 列出可用的subreddit
   - 检查数据文件完整性

2. **数据筛选**
   - 根据股票代码匹配公司名称
   - 按日期范围过滤帖子
   - 检查标题和内容相关性

3. **内容聚合**
   - 收集匹配的帖子
   - 按热度排序
   - 限制每日最大数量

4. **格式化输出**
   - 生成结构化内容
   - 统计帖子数量
   - 计算处理时间

## 内容质量评估

脚本包含自动质量评估功能，评分标准（0-10分）：

### 评分维度

1. **基础长度** (0-3分)
   - 100字符以上: +1分
   - 500字符以上: +1分  
   - 1000字符以上: +1分

2. **内容多样性** (0-3分)
   - 包含金融关键词: +1分
   - 包含情绪关键词: +1分
   - 包含社交关键词: +1分

3. **结构化程度** (0-2分)
   - 有标题结构: +1分
   - 多行内容: +1分

4. **内容真实性** (0-2分)
   - 非指导性文本: +2分

### 质量等级

- **9-10分**: 优秀 - 内容丰富，结构清晰，信息价值高
- **7-8分**: 良好 - 内容较好，有一定分析价值
- **5-6分**: 一般 - 基本可用，但信息量有限
- **3-4分**: 较差 - 内容简单，价值不高
- **0-2分**: 很差 - 无效内容或错误信息

## 输出格式

### JSON格式结果

```json
{
  "success": true,
  "method": "online_api",
  "model": "deepseek-chat",
  "ticker": "AAPL",
  "date": "2024-05-20",
  "content": "详细的社交媒体分析内容...",
  "content_length": 2048,
  "quality_score": 8.5,
  "api_response_time": 3.2,
  "tokens_used": 1500,
  "timestamp": "2024-05-20T10:30:00"
}
```

### 控制台输出示例

```
📊 社交媒体数据获取结果
============================================================
🏷️  股票代码: AAPL
📅 目标日期: 2024-05-20
🔧 获取方式: online_api
✅ 获取状态: 成功
📄 内容长度: 2048 字符
⭐ 质量评分: 8.5/10
⏱️  API响应时间: 3.20秒
🎯 使用Tokens: 1500
🤖 使用模型: deepseek-chat

📖 内容预览 (前500字符):
----------------------------------------
### AAPL 社交媒体情绪分析

**投资者讨论热点:**
1. iPhone 15系列销售表现超预期
2. 服务业务收入持续增长
3. 中国市场复苏迹象明显
...
```

## 环境配置

### 必需的环境变量

```bash
# DeepSeek API (推荐)
DEEPSEEK_API_KEY=your_deepseek_api_key

# 或者 OpenAI API (备选)
OPENAI_API_KEY=your_openai_api_key
```

### 可选配置

```bash
# FinnHub API (用于其他数据)
FINNHUB_API_KEY=your_finnhub_api_key

# Reddit API (未来扩展)
REDDIT_CLIENT_ID=your_reddit_client_id
REDDIT_CLIENT_SECRET=your_reddit_client_secret
```

## 使用示例

详细的使用示例请参考：`examples/social_media_data_example.py`

该示例包含：
- 在线获取示例
- 离线获取示例
- 批量处理示例
- 质量分析示例

## 故障排除

### 常见问题

1. **API密钥错误**
   ```
   ❌ LLM客户端初始化失败: Invalid API key
   ```
   解决方案: 检查.env文件中的API密钥配置

2. **Reddit数据目录不存在**
   ```
   ⚠️ Reddit数据目录不存在: /path/to/reddit_data
   ```
   解决方案: 运行数据下载脚本或检查DATA_DIR配置

3. **网络连接问题**
   ```
   ❌ API请求失败: Connection timeout
   ```
   解决方案: 检查网络连接，或使用离线模式

4. **内容质量低**
   ```
   ⭐ 质量评分: 2.0/10
   ```
   解决方案: 尝试不同的日期或股票，检查数据源质量

### 调试技巧

1. 使用`--debug`参数查看详细日志
2. 检查保存的JSON文件了解详细错误信息
3. 尝试不同的获取模式进行对比
4. 验证股票代码和日期格式的正确性

## 扩展开发

### 添加新的数据源

1. 在`SocialMediaDataFetcher`类中添加新方法
2. 实现数据获取逻辑
3. 更新质量评估标准
4. 添加相应的命令行选项

### 自定义质量评估

可以通过修改`_assess_content_quality`方法来自定义质量评估标准，例如：
- 添加特定行业关键词
- 调整评分权重
- 增加新的评估维度

### 集成到现有系统

脚本设计为独立运行，但也可以作为模块导入：

```python
from scripts.social_media_data_fetcher import SocialMediaDataFetcher

fetcher = SocialMediaDataFetcher(debug=False)
result = fetcher.fetch_online_data("AAPL", "2024-05-20")
```
