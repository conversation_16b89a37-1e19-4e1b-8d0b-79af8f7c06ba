#!/usr/bin/env python3
"""
Reddit数据获取示例

这个示例展示了如何使用Reddit数据获取器来获取和分析股票相关的Reddit讨论数据。
"""

import sys
from pathlib import Path
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from scripts.reddit_data_fetcher import RedditDataFetcher


def example_basic_fetch():
    """基本获取示例"""
    print("📱 基本Reddit数据获取示例")
    print("=" * 40)
    
    # 初始化获取器
    fetcher = RedditDataFetcher(debug=True)
    
    # 获取AAPL的Reddit讨论数据
    ticker = "AAPL"
    date = "2024-05-20"
    
    print(f"获取 {ticker} 在 {date} 的Reddit讨论数据...")
    
    results = fetcher.fetch_reddit_posts(
        ticker=ticker,
        target_date=date,
        days_back=1,
        max_posts_per_subreddit=3
    )
    
    # 显示结果
    fetcher.display_results(results)
    
    return results


def example_multi_day_fetch():
    """多天数据获取示例"""
    print("\n📅 多天Reddit数据获取示例")
    print("=" * 40)
    
    # 初始化获取器
    fetcher = RedditDataFetcher(debug=True)
    
    # 获取TSLA过去3天的Reddit讨论数据
    ticker = "TSLA"
    date = "2024-05-20"
    days = 3
    
    print(f"获取 {ticker} 在 {date} 前 {days} 天的Reddit讨论数据...")
    
    results = fetcher.fetch_reddit_posts(
        ticker=ticker,
        target_date=date,
        days_back=days,
        max_posts_per_subreddit=2
    )
    
    # 显示结果
    fetcher.display_results(results)
    
    # 显示情绪分析
    if results.get('success'):
        sentiment = fetcher.get_sentiment_summary(results)
        print(f"\n📈 情绪分析:")
        print(f"   整体情绪: {sentiment['overall_sentiment']}")
        print(f"   正面帖子: {sentiment['positive_posts']}")
        print(f"   负面帖子: {sentiment['negative_posts']}")
        print(f"   平均赞数: {sentiment['average_upvotes']:.1f}")
    
    return results


def example_batch_analysis():
    """批量分析示例"""
    print("\n📦 批量Reddit数据分析示例")
    print("=" * 40)
    
    # 初始化获取器
    fetcher = RedditDataFetcher(debug=False)  # 减少输出
    
    # 要分析的股票列表
    tickers = ["AAPL", "TSLA", "NVDA", "MSFT"]
    date = "2024-05-20"
    
    results_summary = []
    
    for ticker in tickers:
        print(f"\n处理 {ticker}...")
        
        results = fetcher.fetch_reddit_posts(
            ticker=ticker,
            target_date=date,
            days_back=1,
            max_posts_per_subreddit=3
        )
        
        if results.get('success'):
            sentiment = fetcher.get_sentiment_summary(results)
            
            summary = {
                'ticker': ticker,
                'total_posts': results['total_posts'],
                'sentiment': sentiment['overall_sentiment'],
                'positive_posts': sentiment['positive_posts'],
                'negative_posts': sentiment['negative_posts'],
                'avg_upvotes': sentiment['average_upvotes']
            }
            
            results_summary.append(summary)
            
            print(f"  ✅ {ticker}: {results['total_posts']} 帖子, 情绪: {sentiment['overall_sentiment']}")
        else:
            print(f"  ❌ {ticker}: 获取失败")
    
    # 显示汇总结果
    print(f"\n📊 批量分析汇总:")
    print("-" * 50)
    print(f"{'股票':<8} {'帖子数':<8} {'情绪':<10} {'正面':<6} {'负面':<6} {'平均赞数':<10}")
    print("-" * 50)
    
    for summary in results_summary:
        print(f"{summary['ticker']:<8} {summary['total_posts']:<8} {summary['sentiment']:<10} "
              f"{summary['positive_posts']:<6} {summary['negative_posts']:<6} {summary['avg_upvotes']:<10.1f}")
    
    return results_summary


def example_search_terms():
    """搜索关键词示例"""
    print("\n🔍 搜索关键词映射示例")
    print("=" * 40)
    
    fetcher = RedditDataFetcher(debug=False)
    
    # 测试不同股票的搜索关键词
    test_tickers = ["AAPL", "TSLA", "META", "TSM", "GOOGL"]
    
    print("股票代码 -> 搜索关键词:")
    print("-" * 30)
    
    for ticker in test_tickers:
        search_terms = fetcher.get_company_search_terms(ticker)
        print(f"{ticker:<8} -> {', '.join(search_terms)}")
    
    return test_tickers


def example_data_structure():
    """数据结构示例"""
    print("\n📁 Reddit数据结构示例")
    print("=" * 40)
    
    fetcher = RedditDataFetcher(debug=True)
    
    # 显示数据目录结构
    reddit_dir = fetcher.reddit_data_dir
    print(f"Reddit数据目录: {reddit_dir}")
    
    if reddit_dir.exists():
        company_news_dir = reddit_dir / "company_news"
        if company_news_dir.exists():
            print(f"\n可用的subreddit:")
            for subreddit_dir in company_news_dir.iterdir():
                if subreddit_dir.is_dir():
                    jsonl_files = list(subreddit_dir.glob("*.jsonl"))
                    print(f"  {subreddit_dir.name}: {len(jsonl_files)} 个数据文件")
                    
                    # 显示文件示例
                    if jsonl_files:
                        sample_file = jsonl_files[0]
                        print(f"    示例文件: {sample_file.name}")
    else:
        print("数据目录不存在，将创建示例结构")


def example_save_and_load():
    """保存和加载示例"""
    print("\n💾 保存和加载示例")
    print("=" * 40)
    
    fetcher = RedditDataFetcher(debug=False)
    
    # 获取数据
    results = fetcher.fetch_reddit_posts(
        ticker="AAPL",
        target_date="2024-05-20",
        days_back=1,
        max_posts_per_subreddit=2
    )
    
    if results.get('success'):
        # 保存结果
        filepath = fetcher.save_results(results)
        print(f"✅ 结果已保存到: {filepath}")
        
        # 验证文件存在
        if filepath.exists():
            file_size = filepath.stat().st_size
            print(f"📄 文件大小: {file_size} 字节")
            
            # 读取并验证保存的数据
            import json
            with open(filepath, 'r', encoding='utf-8') as f:
                loaded_data = json.load(f)
            
            print(f"📊 加载的数据包含 {loaded_data.get('total_posts', 0)} 个帖子")
        else:
            print("❌ 文件保存失败")
    else:
        print("❌ 数据获取失败，无法保存")


def main():
    """主函数"""
    print("🚀 Reddit数据获取示例")
    print("=" * 50)
    
    try:
        # 1. 基本获取示例
        example_basic_fetch()
        
        # 2. 多天数据获取示例
        example_multi_day_fetch()
        
        # 3. 批量分析示例
        example_batch_analysis()
        
        # 4. 搜索关键词示例
        example_search_terms()
        
        # 5. 数据结构示例
        example_data_structure()
        
        # 6. 保存和加载示例
        example_save_and_load()
        
        print("\n🎯 所有示例执行完成!")
        print("\n💡 使用提示:")
        print("1. 如果没有真实Reddit数据，脚本会自动创建示例数据")
        print("2. 可以通过命令行直接使用: python scripts/reddit_data_fetcher.py --help")
        print("3. 支持多种参数组合，详见帮助信息")
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断执行")
    except Exception as e:
        print(f"\n❌ 执行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
