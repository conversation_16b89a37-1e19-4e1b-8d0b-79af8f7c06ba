#!/usr/bin/env python3
"""
测试Reddit数据获取修复
验证新的搜索策略是否能获取到数据
"""

import praw
import json
import time
import os
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional
import logging
from collections import defaultdict
import re


class RedditFixTester:
    """Reddit修复测试器"""
    
    def __init__(self):
        """初始化测试器"""
        # 设置日志
        self.setup_logging()
        
        # 初始化Reddit API
        self.setup_reddit_api()
        
        # 测试股票配置
        self.test_stocks = {
            'AAPL': {
                'name': 'Apple',
                'keywords': ['AAPL', 'Apple', 'iPhone'],
                'folder': 'AAPL_Reddit_Test'
            }
        }
        
        # 测试subreddit列表（减少数量加快测试）
        self.subreddits = ['stocks', 'investing', 'wallstreetbets']
        
        # 情绪分析关键词
        self.sentiment_keywords = {
            'positive': [
                'bullish', 'buy', 'moon', 'rocket', 'strong', 'good', 'great',
                'excellent', 'positive', 'up', 'rise', 'gain', 'profit', 'bull'
            ],
            'negative': [
                'bearish', 'sell', 'short', 'weak', 'bad', 'terrible',
                'negative', 'down', 'fall', 'loss', 'crash', 'bear'
            ]
        }
    
    def setup_logging(self):
        """设置日志系统"""
        import sys
        
        if sys.stdout.encoding != 'utf-8':
            import io
            sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')

        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('reddit_fix_test.log', encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def setup_reddit_api(self):
        """设置Reddit API连接"""
        try:
            self.reddit = praw.Reddit(
                client_id="vyg0MLjDgpqYQRNHaJaJEQ",
                client_secret="hy412_1dYvB0xXKe5tUWYCTSeFZpGQ",
                user_agent="ai_fund_hedge by /u/Available_Neck_1936"
            )
            
            # 测试连接
            self.reddit.user.me()
            self.logger.info("✅ Reddit API连接成功")
            
        except Exception as e:
            self.logger.error(f"❌ Reddit API连接失败: {e}")
            raise
    
    def calculate_sentiment_score(self, text: str) -> Dict[str, Any]:
        """计算情绪得分"""
        text_lower = text.lower()
        
        positive_count = sum(1 for keyword in self.sentiment_keywords['positive'] 
                           if keyword in text_lower)
        negative_count = sum(1 for keyword in self.sentiment_keywords['negative'] 
                           if keyword in text_lower)
        
        total_keywords = positive_count + negative_count
        if total_keywords == 0:
            sentiment_score = 0.0
            sentiment_label = 'neutral'
        else:
            sentiment_score = (positive_count - negative_count) / total_keywords
            if sentiment_score > 0.2:
                sentiment_label = 'positive'
            elif sentiment_score < -0.2:
                sentiment_label = 'negative'
            else:
                sentiment_label = 'neutral'
        
        return {
            'score': round(sentiment_score, 3),
            'label': sentiment_label,
            'positive_keywords': positive_count,
            'negative_keywords': negative_count,
            'total_keywords': total_keywords
        }
    
    def is_relevant_post(self, post, keywords: List[str]) -> bool:
        """检查帖子是否与股票相关"""
        title = post.title.lower()
        content = post.selftext.lower()
        
        for keyword in keywords:
            if keyword.lower() in title or keyword.lower() in content:
                return True
        
        return False
    
    def test_search_strategies(self, ticker: str = 'AAPL') -> Dict[str, Any]:
        """测试不同的搜索策略"""
        self.logger.info(f"\n🧪 测试 {ticker} 的搜索策略...")
        
        config = self.test_stocks[ticker]
        keywords = config['keywords']
        
        results = {
            'ticker': ticker,
            'strategies': {},
            'total_posts_found': 0,
            'unique_posts': 0
        }
        
        all_post_ids = set()
        
        # 测试不同的搜索策略
        strategies = [
            {"name": "week_relevance", "time_filter": "week", "sort": "relevance", "limit": 20},
            {"name": "month_hot", "time_filter": "month", "sort": "hot", "limit": 15},
            {"name": "year_top", "time_filter": "year", "sort": "top", "limit": 10},
            {"name": "all_new", "time_filter": "all", "sort": "new", "limit": 10}
        ]
        
        for strategy in strategies:
            strategy_name = strategy['name']
            self.logger.info(f"   🔍 测试策略: {strategy_name}")
            
            strategy_results = {
                'posts_found': 0,
                'relevant_posts': 0,
                'subreddit_breakdown': {}
            }
            
            for subreddit_name in self.subreddits:
                try:
                    subreddit = self.reddit.subreddit(subreddit_name)
                    subreddit_posts = 0
                    
                    for keyword in keywords:
                        try:
                            search_results = subreddit.search(
                                keyword,
                                time_filter=strategy['time_filter'],
                                sort=strategy['sort'],
                                limit=strategy['limit'] // len(keywords)
                            )
                            
                            for post in search_results:
                                strategy_results['posts_found'] += 1
                                subreddit_posts += 1
                                all_post_ids.add(post.id)
                                
                                if self.is_relevant_post(post, keywords):
                                    strategy_results['relevant_posts'] += 1
                            
                            time.sleep(0.5)
                            
                        except Exception as e:
                            self.logger.warning(f"     ⚠️ 搜索失败: {keyword} in {subreddit_name}: {e}")
                            continue
                    
                    strategy_results['subreddit_breakdown'][subreddit_name] = subreddit_posts
                    time.sleep(1)
                    
                except Exception as e:
                    self.logger.warning(f"     ❌ subreddit {subreddit_name} 访问失败: {e}")
                    continue
            
            results['strategies'][strategy_name] = strategy_results
            results['total_posts_found'] += strategy_results['posts_found']
            
            self.logger.info(f"     📊 {strategy_name}: {strategy_results['posts_found']} 帖子, {strategy_results['relevant_posts']} 相关")
        
        results['unique_posts'] = len(all_post_ids)
        
        return results
    
    def test_recent_posts(self, ticker: str = 'AAPL', days_back: int = 7) -> List[Dict]:
        """测试获取最近几天的帖子"""
        self.logger.info(f"\n📅 测试获取 {ticker} 最近 {days_back} 天的帖子...")
        
        config = self.test_stocks[ticker]
        keywords = config['keywords']
        
        # 计算日期范围
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days_back)
        
        self.logger.info(f"   📆 时间范围: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
        
        all_posts = []
        
        for subreddit_name in self.subreddits:
            try:
                subreddit = self.reddit.subreddit(subreddit_name)
                
                for keyword in keywords:
                    try:
                        search_results = subreddit.search(
                            keyword,
                            time_filter="week",
                            sort="new",
                            limit=15
                        )
                        
                        for post in search_results:
                            post_date = datetime.fromtimestamp(post.created_utc)
                            
                            # 检查日期范围
                            if start_date <= post_date <= end_date:
                                if self.is_relevant_post(post, keywords):
                                    full_text = f"{post.title} {post.selftext}"
                                    sentiment = self.calculate_sentiment_score(full_text)
                                    
                                    post_data = {
                                        'id': post.id,
                                        'title': post.title,
                                        'content': post.selftext[:200],
                                        'score': post.score,
                                        'num_comments': post.num_comments,
                                        'created_date': post_date.strftime("%Y-%m-%d %H:%M:%S"),
                                        'subreddit': subreddit_name,
                                        'search_keyword': keyword,
                                        'sentiment': sentiment
                                    }
                                    
                                    all_posts.append(post_data)
                        
                        time.sleep(0.5)
                        
                    except Exception as e:
                        self.logger.warning(f"     ⚠️ 搜索失败: {keyword}: {e}")
                        continue
                
                time.sleep(1)
                
            except Exception as e:
                self.logger.warning(f"     ❌ subreddit {subreddit_name} 失败: {e}")
                continue
        
        # 去重
        seen_ids = set()
        unique_posts = []
        for post in all_posts:
            if post['id'] not in seen_ids:
                seen_ids.add(post['id'])
                unique_posts.append(post)
        
        self.logger.info(f"   ✅ 找到 {len(unique_posts)} 个相关帖子")
        
        return unique_posts
    
    def run_comprehensive_test(self):
        """运行综合测试"""
        self.logger.info("🚀 开始Reddit修复综合测试")
        self.logger.info("=" * 50)
        
        # 测试1: 搜索策略测试
        strategy_results = self.test_search_strategies('AAPL')
        
        # 测试2: 最近帖子测试
        recent_posts = self.test_recent_posts('AAPL', 7)
        
        # 生成测试报告
        report = {
            'test_time': datetime.now().isoformat(),
            'strategy_test': strategy_results,
            'recent_posts_test': {
                'posts_found': len(recent_posts),
                'sample_posts': recent_posts[:5] if recent_posts else []
            },
            'recommendations': []
        }
        
        # 分析结果并给出建议
        if strategy_results['total_posts_found'] == 0:
            report['recommendations'].append("❌ 所有搜索策略都没有找到帖子，可能需要检查API配置或关键词")
        elif strategy_results['total_posts_found'] < 10:
            report['recommendations'].append("⚠️ 找到的帖子数量较少，建议扩展关键词或时间范围")
        else:
            report['recommendations'].append("✅ 搜索策略有效，找到了足够的帖子")
        
        if len(recent_posts) == 0:
            report['recommendations'].append("❌ 最近几天没有找到相关帖子，可能需要调整搜索参数")
        else:
            report['recommendations'].append(f"✅ 最近7天找到 {len(recent_posts)} 个相关帖子")
        
        # 保存测试报告
        with open('reddit_fix_test_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        # 输出总结
        self.logger.info("\n📋 测试总结:")
        self.logger.info(f"   搜索策略测试: 找到 {strategy_results['total_posts_found']} 个帖子")
        self.logger.info(f"   最近帖子测试: 找到 {len(recent_posts)} 个帖子")
        
        for rec in report['recommendations']:
            self.logger.info(f"   {rec}")
        
        self.logger.info(f"\n📄 详细报告已保存: reddit_fix_test_report.json")
        
        return report


def main():
    """主函数"""
    print("🧪 Reddit数据获取修复测试")
    print("=" * 40)
    
    try:
        tester = RedditFixTester()
        report = tester.run_comprehensive_test()
        
        print("\n✅ 测试完成!")
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
