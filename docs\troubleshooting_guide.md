# TradingAgents 数据获取故障排除指南

## 概述

本指南帮助解决在使用TradingAgents在线数据获取工具时遇到的常见问题。

## 常见问题及解决方案

### 1. API速率限制问题

**问题症状**:
```
❌ 获取 AAPL 数据失败: Too Many Requests. Rate limited. Try after a while.
```

**原因分析**:
- API调用频率过高，触发了服务商的速率限制
- 默认延迟时间太短（1秒）不足以避免速率限制

**解决方案**:

#### 方案A: 增加API调用延迟
```bash
# 将延迟增加到3-5秒
python scripts/fetch_online_data.py --symbols AAPL --start-date 2025-01-01 --end-date 2025-01-31 --delay 5.0
```

#### 方案B: 使用更短的时间范围
```bash
# 使用更短的时间范围减少API调用次数
python scripts/fetch_online_data.py --symbols AAPL --start-date 2024-01-25 --end-date 2024-01-31 --delay 3.0
```

#### 方案C: 分批获取数据
```bash
# 分别获取不同类型的数据
python scripts/fetch_online_data.py --symbols AAPL --start-date 2024-01-01 --end-date 2024-01-31 --analysts market --delay 3.0
python scripts/fetch_online_data.py --symbols AAPL --start-date 2024-01-01 --end-date 2024-01-31 --analysts news --delay 3.0
```

### 2. 数据质量问题

**问题症状**:
- LLM返回指导性文本而非实际数据
- 新闻内容包含"search for"、"you can use"等指导性语言
- 基本面数据缺少实际财务数字

**原因分析**:
- LLM模型将请求理解为寻求指导而非获取实际数据
- 请求的日期可能超出了模型的知识截止日期
- 提示词可能需要优化

**解决方案**:

#### 方案A: 使用历史日期
```bash
# 使用2024年的历史日期而非2025年
python scripts/fetch_online_data.py --symbols AAPL --start-date 2024-01-01 --end-date 2024-01-31
```

#### 方案B: 使用调试脚本检查数据质量
```bash
# 测试单个组件的数据质量
python scripts/debug_data_fetch.py --test news --symbol AAPL --date 2024-01-15
python scripts/debug_data_fetch.py --test fundamentals --symbol AAPL --date 2024-01-15
```

#### 方案C: 检查改进后的脚本
新版本的脚本已经添加了数据质量检查，会自动识别和标记低质量内容。

### 3. Google新闻数据为空

**问题症状**:
```json
"AAPL_google_news": {
  "file": "...",
  "content": ""
}
```

**原因分析**:
- Google新闻API可能有访问限制
- 网络连接问题
- 搜索查询没有返回结果

**解决方案**:

#### 方案A: 检查网络连接
```bash
# 测试网络连接
ping google.com
```

#### 方案B: 使用调试脚本测试
```bash
python scripts/debug_data_fetch.py --test news --symbol AAPL --date 2024-01-15
```

#### 方案C: 跳过Google新闻
如果Google新闻持续失败，可以只获取其他类型的数据：
```bash
python scripts/fetch_online_data.py --symbols AAPL --start-date 2024-01-01 --end-date 2024-01-31 --analysts market,social,fundamentals
```

## 最佳实践建议

### 1. 渐进式测试

**步骤1: 环境检查**
```bash
python scripts/debug_data_fetch.py --test all --symbol AAPL --date 2024-01-15
```

**步骤2: 单个股票测试**
```bash
python scripts/fetch_online_data.py --symbols AAPL --start-date 2024-01-15 --end-date 2024-01-15 --delay 5.0 --verbose
```

**步骤3: 扩展到更长时间范围**
```bash
python scripts/fetch_online_data.py --symbols AAPL --start-date 2024-01-01 --end-date 2024-01-07 --delay 3.0
```

### 2. 合理的API调用策略

**推荐配置**:
- **延迟时间**: 3-5秒
- **时间范围**: 开始时使用1-7天
- **股票数量**: 开始时使用1-2个股票
- **重试次数**: 3次
- **重试延迟**: 5秒（普通错误），60-120秒（速率限制）

### 3. 成本控制

**估算API调用次数**:
- 市场数据: ~6次/股票（价格数据 + 5个技术指标）
- 新闻数据: ~2次/日期（全球新闻 + 每个股票的Google新闻）
- 社交媒体数据: ~1次/股票
- 基本面数据: ~1次/股票

**总计**: 约10次API调用/股票/日期

**建议**:
- 开发测试时使用短时间范围
- 生产环境分批获取数据
- 监控API使用量和成本

### 4. 数据验证

**自动验证**:
新版本脚本已包含以下验证：
- 内容长度检查
- 指导性文本检测
- 财务指标存在性检查
- 数据格式验证

**手动验证**:
```bash
# 检查生成的汇总文件
cat data/data_summary_2024-01-31.json

# 检查具体数据文件
head -20 data/news_data/global/global-news-2024-01-31.txt
```

## 配置优化

### 1. 环境变量配置

确保正确配置API密钥：
```bash
# .env文件内容
DEEPSEEK_API_KEY=your_actual_key_here
FINNHUB_API_KEY=your_actual_key_here
```

### 2. 脚本参数优化

**保守配置**（适合初次使用）:
```bash
python scripts/fetch_online_data.py \
  --symbols AAPL \
  --start-date 2024-01-15 \
  --end-date 2024-01-15 \
  --delay 5.0 \
  --verbose
```

**生产配置**（适合批量获取）:
```bash
python scripts/fetch_online_data.py \
  --symbols AAPL,MSFT \
  --start-date 2024-01-01 \
  --end-date 2024-01-07 \
  --delay 3.0 \
  --analysts market,fundamentals
```

### 3. 批量配置文件优化

编辑 `scripts/data_fetch_config.yaml`:
```yaml
# 基本配置
delay: 5.0  # 增加延迟避免速率限制

# API调用配置
api_settings:
  max_retries: 3
  retry_delay: 10.0
  rate_limit_delay: 180  # 速率限制时等待3分钟

# 使用历史日期
date_ranges:
  - start_date: "2024-01-01"
    end_date: "2024-01-07"
    name: "2024_week1"
```

## 故障诊断工具

### 1. 调试脚本
```bash
# 综合测试
python scripts/debug_data_fetch.py --test all --symbol AAPL --date 2024-01-15

# 单项测试
python scripts/debug_data_fetch.py --test market --symbol AAPL --date 2024-01-15
```

### 2. 日志分析
```bash
# 查看详细输出
python scripts/fetch_online_data.py --symbols AAPL --start-date 2024-01-15 --end-date 2024-01-15 --verbose

# 检查错误信息
grep -i error data/data_summary_*.json
```

### 3. 数据验证
```bash
# 检查文件大小
find data/ -name "*.txt" -exec wc -c {} \;

# 检查内容质量
grep -i "search for\|you can use\|here's how" data/**/*.txt
```

## 联系支持

如果问题持续存在：

1. **收集信息**:
   - 错误消息的完整输出
   - 使用的命令和参数
   - API密钥配置状态（不要包含实际密钥）
   - 生成的汇总文件内容

2. **尝试调试脚本**:
   ```bash
   python scripts/debug_data_fetch.py --test all --symbol AAPL --date 2024-01-15
   ```

3. **检查相关文档**:
   - [在线数据获取指南](online_data_fetching_guide.md)
   - [分析师数据来源说明](analysts_data_sources.md)

---

*最后更新: 2025-01-25*
