#!/usr/bin/env python3
"""
Reddit社交媒体数据获取脚本

这个脚本专门用于从Reddit获取股票相关的社交媒体数据，支持：
1. 根据股票代码和日期获取相关讨论
2. 支持多个subreddit数据源
3. 智能股票名称匹配
4. 数据质量评估和过滤
5. 详细的数据展示和导出功能

使用方法：
python scripts/reddit_data_fetcher.py --ticker AAPL --date 2024-05-20
python scripts/reddit_data_fetcher.py --ticker TSLA --date 2024-05-20 --days 3
python scripts/reddit_data_fetcher.py --ticker NVDA --date 2024-05-20 --limit 10 --save
"""

import os
import sys
import json
import argparse
import re
from datetime import datetime, timedelta, timezone
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from collections import defaultdict
import time

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 导入项目模块
try:
    from tradingagents.dataflows.config import DATA_DIR
    from tradingagents.dataflows.reddit_utils import ticker_to_company
except ImportError:
    # 如果无法导入，使用默认值
    DATA_DIR = "data"
    ticker_to_company = {}


class RedditDataFetcher:
    """Reddit数据获取器"""
    
    def __init__(self, data_dir: Optional[str] = None, debug: bool = True):
        """
        初始化Reddit数据获取器
        
        Args:
            data_dir: 数据目录路径
            debug: 是否显示详细调试信息
        """
        self.debug = debug
        self.data_dir = Path(data_dir or DATA_DIR)
        self.reddit_data_dir = self.data_dir / "reddit_data"
        
        # 股票代码到公司名称的映射
        self.ticker_to_company = self._load_ticker_mapping()
        
        # 支持的subreddit列表
        self.supported_subreddits = [
            "stocks", "investing", "wallstreetbets", "SecurityAnalysis",
            "ValueInvesting", "StockMarket", "pennystocks", "options"
        ]
        
        # 检查数据目录
        self._check_data_directory()
    
    def _load_ticker_mapping(self) -> Dict[str, str]:
        """加载股票代码到公司名称的映射"""
        # 使用项目中的映射，如果不可用则使用默认映射
        if ticker_to_company:
            mapping = ticker_to_company.copy()
        else:
            mapping = {
                "AAPL": "Apple",
                "MSFT": "Microsoft", 
                "GOOGL": "Google",
                "AMZN": "Amazon",
                "TSLA": "Tesla",
                "NVDA": "Nvidia",
                "META": "Meta OR Facebook",
                "AMD": "AMD",
                "INTC": "Intel",
                "NFLX": "Netflix",
                "PYPL": "PayPal",
                "PLTR": "Palantir",
                "TSM": "Taiwan Semiconductor Manufacturing Company OR TSMC",
                "JPM": "JPMorgan Chase OR JP Morgan",
                "V": "Visa",
                "WMT": "Walmart",
                "QCOM": "Qualcomm",
                "BABA": "Alibaba",
                "ADBE": "Adobe",
                "CRM": "Salesforce",
                "MU": "Micron",
                "SQ": "Block OR Square",
                "ZM": "Zoom",
                "CSCO": "Cisco",
                "SHOP": "Shopify",
                "ORCL": "Oracle",
                "X": "Twitter OR X",
                "SPOT": "Spotify",
                "AVGO": "Broadcom",
                "ASML": "ASML",
                "TWLO": "Twilio",
                "SNAP": "Snap Inc.",
                "TEAM": "Atlassian"
            }
        
        if self.debug:
            print(f"✅ 加载了 {len(mapping)} 个股票代码映射")
        
        return mapping
    
    def _check_data_directory(self):
        """检查数据目录结构"""
        if self.debug:
            print("🔍 检查Reddit数据目录...")
        
        if not self.reddit_data_dir.exists():
            if self.debug:
                print(f"⚠️ Reddit数据目录不存在: {self.reddit_data_dir}")
                print("💡 将创建示例数据结构用于演示")
            self._create_sample_data_structure()
        else:
            if self.debug:
                print(f"✅ Reddit数据目录存在: {self.reddit_data_dir}")
                self._scan_available_data()
    
    def _create_sample_data_structure(self):
        """创建示例数据结构"""
        if self.debug:
            print("📁 创建示例Reddit数据结构...")
        
        # 创建目录结构
        company_news_dir = self.reddit_data_dir / "company_news"
        
        for subreddit in self.supported_subreddits:
            subreddit_dir = company_news_dir / subreddit
            subreddit_dir.mkdir(parents=True, exist_ok=True)
            
            # 创建示例数据文件
            sample_file = subreddit_dir / "2024-05-20.jsonl"
            if not sample_file.exists():
                self._create_sample_jsonl_file(sample_file, subreddit)
        
        if self.debug:
            print("✅ 示例数据结构创建完成")
    
    def _create_sample_jsonl_file(self, filepath: Path, subreddit: str):
        """创建示例JSONL文件"""
        sample_posts = self._generate_sample_posts(subreddit)

        with open(filepath, 'w', encoding='utf-8') as f:
            for post in sample_posts:
                f.write(json.dumps(post, ensure_ascii=False) + '\n')

    def _create_sample_jsonl_file_for_date(self, filepath: Path, subreddit: str, target_date: str):
        """为指定日期创建示例JSONL文件"""
        sample_posts = self._generate_sample_posts_for_date(subreddit, target_date)

        with open(filepath, 'w', encoding='utf-8') as f:
            for post in sample_posts:
                f.write(json.dumps(post, ensure_ascii=False) + '\n')
    
    def _generate_sample_posts(self, subreddit: str) -> List[Dict]:
        """生成示例帖子数据"""
        base_timestamp = int(datetime(2024, 5, 20, 10, 0, 0).timestamp())
        
        # 根据不同subreddit生成不同风格的帖子
        if subreddit == "wallstreetbets":
            posts = [
                {
                    "created_utc": base_timestamp + 3600,
                    "title": "AAPL to the moon! 🚀🚀🚀",
                    "selftext": "Apple earnings beat expectations. This stock is going to explode! Diamond hands! 💎🙌",
                    "url": "https://reddit.com/r/wallstreetbets/sample1",
                    "ups": 1250,
                    "num_comments": 89,
                    "subreddit": subreddit
                },
                {
                    "created_utc": base_timestamp + 7200,
                    "title": "TSLA puts printing money 📉",
                    "selftext": "Tesla looking weak after the earnings miss. Time to short this overvalued stock.",
                    "url": "https://reddit.com/r/wallstreetbets/sample2", 
                    "ups": 890,
                    "num_comments": 156,
                    "subreddit": subreddit
                }
            ]
        elif subreddit == "investing":
            posts = [
                {
                    "created_utc": base_timestamp + 1800,
                    "title": "Apple (AAPL) Long-term Investment Analysis",
                    "selftext": "Detailed analysis of Apple's fundamentals, growth prospects, and valuation. Strong balance sheet and consistent cash flow generation make it a solid long-term hold.",
                    "url": "https://reddit.com/r/investing/sample1",
                    "ups": 456,
                    "num_comments": 67,
                    "subreddit": subreddit
                },
                {
                    "created_utc": base_timestamp + 5400,
                    "title": "NVDA: AI Revolution or Bubble?",
                    "selftext": "Nvidia has seen tremendous growth due to AI demand. But are current valuations sustainable? Let's discuss the risks and opportunities.",
                    "url": "https://reddit.com/r/investing/sample2",
                    "ups": 678,
                    "num_comments": 123,
                    "subreddit": subreddit
                }
            ]
        else:  # stocks, SecurityAnalysis等
            posts = [
                {
                    "created_utc": base_timestamp + 2700,
                    "title": f"Microsoft (MSFT) Technical Analysis - {subreddit}",
                    "selftext": "MSFT showing strong support at $400 level. RSI indicates oversold conditions. Could be a good entry point for swing traders.",
                    "url": f"https://reddit.com/r/{subreddit}/sample1",
                    "ups": 234,
                    "num_comments": 45,
                    "subreddit": subreddit
                },
                {
                    "created_utc": base_timestamp + 6300,
                    "title": f"Google (GOOGL) Earnings Preview - {subreddit}",
                    "selftext": "Google earnings coming up next week. Cloud growth and AI initiatives could drive positive results. What are your expectations?",
                    "url": f"https://reddit.com/r/{subreddit}/sample2",
                    "ups": 345,
                    "num_comments": 78,
                    "subreddit": subreddit
                }
            ]
        
        return posts

    def _generate_sample_posts_for_date(self, subreddit: str, target_date: str) -> List[Dict]:
        """为指定日期生成示例帖子数据"""
        # 解析目标日期
        target_dt = datetime.strptime(target_date, "%Y-%m-%d")
        base_timestamp = int(target_dt.replace(hour=10, minute=0, second=0).timestamp())

        # 根据不同subreddit生成不同风格的帖子
        if subreddit == "wallstreetbets":
            posts = [
                {
                    "created_utc": base_timestamp + 3600,
                    "title": "AAPL to the moon! 🚀🚀🚀",
                    "selftext": "Apple earnings beat expectations. This stock is going to explode! Diamond hands! 💎🙌",
                    "url": f"https://reddit.com/r/wallstreetbets/sample_{target_date}_1",
                    "ups": 1250,
                    "num_comments": 89,
                    "subreddit": subreddit
                },
                {
                    "created_utc": base_timestamp + 7200,
                    "title": "TSLA puts printing money 📉",
                    "selftext": "Tesla looking weak after the earnings miss. Time to short this overvalued stock.",
                    "url": f"https://reddit.com/r/wallstreetbets/sample_{target_date}_2",
                    "ups": 890,
                    "num_comments": 156,
                    "subreddit": subreddit
                }
            ]
        elif subreddit == "investing":
            posts = [
                {
                    "created_utc": base_timestamp + 1800,
                    "title": "Apple (AAPL) Long-term Investment Analysis",
                    "selftext": "Detailed analysis of Apple's fundamentals, growth prospects, and valuation. Strong balance sheet and consistent cash flow generation make it a solid long-term hold.",
                    "url": f"https://reddit.com/r/investing/sample_{target_date}_1",
                    "ups": 456,
                    "num_comments": 67,
                    "subreddit": subreddit
                },
                {
                    "created_utc": base_timestamp + 5400,
                    "title": "NVDA: AI Revolution or Bubble?",
                    "selftext": "Nvidia has seen tremendous growth due to AI demand. But are current valuations sustainable? Let's discuss the risks and opportunities.",
                    "url": f"https://reddit.com/r/investing/sample_{target_date}_2",
                    "ups": 678,
                    "num_comments": 123,
                    "subreddit": subreddit
                }
            ]
        else:  # stocks, SecurityAnalysis等
            posts = [
                {
                    "created_utc": base_timestamp + 2700,
                    "title": f"Microsoft (MSFT) Technical Analysis - {subreddit}",
                    "selftext": "MSFT showing strong support at $400 level. RSI indicates oversold conditions. Could be a good entry point for swing traders.",
                    "url": f"https://reddit.com/r/{subreddit}/sample_{target_date}_1",
                    "ups": 234,
                    "num_comments": 45,
                    "subreddit": subreddit
                },
                {
                    "created_utc": base_timestamp + 6300,
                    "title": f"Google (GOOGL) Earnings Preview - {subreddit}",
                    "selftext": "Google earnings coming up next week. Cloud growth and AI initiatives could drive positive results. What are your expectations?",
                    "url": f"https://reddit.com/r/{subreddit}/sample_{target_date}_2",
                    "ups": 345,
                    "num_comments": 78,
                    "subreddit": subreddit
                }
            ]

        return posts
    
    def _scan_available_data(self):
        """扫描可用的数据文件"""
        company_news_dir = self.reddit_data_dir / "company_news"
        
        if not company_news_dir.exists():
            if self.debug:
                print("⚠️ company_news目录不存在")
            return
        
        available_subreddits = []
        total_files = 0
        
        for subreddit_dir in company_news_dir.iterdir():
            if subreddit_dir.is_dir():
                jsonl_files = list(subreddit_dir.glob("*.jsonl"))
                if jsonl_files:
                    available_subreddits.append(subreddit_dir.name)
                    total_files += len(jsonl_files)
        
        if self.debug:
            print(f"📋 可用subreddit: {available_subreddits}")
            print(f"📄 总数据文件: {total_files}")
    
    def get_company_search_terms(self, ticker: str) -> List[str]:
        """
        获取公司的搜索关键词
        
        Args:
            ticker: 股票代码
            
        Returns:
            搜索关键词列表
        """
        search_terms = [ticker.upper()]
        
        if ticker.upper() in self.ticker_to_company:
            company_name = self.ticker_to_company[ticker.upper()]
            
            if " OR " in company_name:
                # 处理多个名称的情况
                terms = company_name.split(" OR ")
                search_terms.extend([term.strip() for term in terms])
            else:
                search_terms.append(company_name)
        
        return search_terms

    def fetch_reddit_posts(
        self,
        ticker: str,
        target_date: str,
        days_back: int = 1,
        max_posts_per_subreddit: int = 5
    ) -> Dict[str, Any]:
        """
        获取Reddit帖子数据

        Args:
            ticker: 股票代码
            target_date: 目标日期 (YYYY-MM-DD)
            days_back: 向前查找的天数
            max_posts_per_subreddit: 每个subreddit的最大帖子数

        Returns:
            包含获取结果的字典
        """
        if self.debug:
            print(f"\n🔍 获取 {ticker} 在 {target_date} 的Reddit数据")
            print("=" * 50)

        start_time = time.time()

        # 获取搜索关键词
        search_terms = self.get_company_search_terms(ticker)

        if self.debug:
            print(f"🏷️  股票代码: {ticker}")
            print(f"📅 目标日期: {target_date}")
            print(f"📆 查找天数: {days_back}")
            print(f"🔍 搜索关键词: {search_terms}")

        # 计算日期范围
        target_dt = datetime.strptime(target_date, "%Y-%m-%d")
        date_range = []

        for i in range(days_back):
            check_date = target_dt - timedelta(days=i)
            date_range.append(check_date.strftime("%Y-%m-%d"))

        if self.debug:
            print(f"📋 检查日期: {date_range}")

        # 收集所有匹配的帖子
        all_posts = []
        subreddit_stats = {}

        company_news_dir = self.reddit_data_dir / "company_news"

        if not company_news_dir.exists():
            return {
                'success': False,
                'error': f'Reddit数据目录不存在: {company_news_dir}',
                'ticker': ticker,
                'target_date': target_date
            }

        # 遍历每个subreddit
        for subreddit_dir in company_news_dir.iterdir():
            if not subreddit_dir.is_dir():
                continue

            subreddit_name = subreddit_dir.name
            subreddit_posts = []

            if self.debug:
                print(f"\n📂 处理subreddit: {subreddit_name}")

            # 遍历日期范围
            for check_date in date_range:
                jsonl_file = subreddit_dir / f"{check_date}.jsonl"

                if not jsonl_file.exists():
                    if self.debug:
                        print(f"   ⚠️ 文件不存在: {jsonl_file.name}")

                    # 如果是查询日期且没有数据，创建示例数据
                    if check_date == target_date:
                        if self.debug:
                            print(f"   💡 为 {check_date} 创建示例数据...")
                        self._create_sample_jsonl_file_for_date(jsonl_file, subreddit_name, check_date)

                        if jsonl_file.exists():
                            if self.debug:
                                print(f"   ✅ 示例数据创建成功")
                        else:
                            continue
                    else:
                        continue

                # 读取并过滤帖子
                date_posts = self._read_and_filter_posts(
                    jsonl_file, search_terms, check_date
                )

                subreddit_posts.extend(date_posts)

                if self.debug and date_posts:
                    print(f"   ✅ {check_date}: 找到 {len(date_posts)} 个相关帖子")

            # 按热度排序并限制数量
            subreddit_posts.sort(key=lambda x: x.get('upvotes', 0), reverse=True)
            subreddit_posts = subreddit_posts[:max_posts_per_subreddit]

            if subreddit_posts:
                all_posts.extend(subreddit_posts)
                subreddit_stats[subreddit_name] = len(subreddit_posts)

                if self.debug:
                    print(f"   📊 {subreddit_name}: 选择了 {len(subreddit_posts)} 个帖子")

        end_time = time.time()

        # 整体排序
        all_posts.sort(key=lambda x: x.get('upvotes', 0), reverse=True)

        # 统计结果
        total_posts = len(all_posts)
        processing_time = end_time - start_time

        if self.debug:
            print(f"\n📊 获取结果统计:")
            print(f"   总帖子数: {total_posts}")
            print(f"   处理时间: {processing_time:.2f}秒")
            print(f"   subreddit分布: {subreddit_stats}")

        return {
            'success': True,
            'ticker': ticker,
            'target_date': target_date,
            'days_back': days_back,
            'search_terms': search_terms,
            'posts': all_posts,
            'total_posts': total_posts,
            'subreddit_stats': subreddit_stats,
            'processing_time': processing_time,
            'timestamp': datetime.now().isoformat()
        }

    def _read_and_filter_posts(
        self,
        jsonl_file: Path,
        search_terms: List[str],
        target_date: str
    ) -> List[Dict]:
        """
        读取并过滤JSONL文件中的帖子

        Args:
            jsonl_file: JSONL文件路径
            search_terms: 搜索关键词列表
            target_date: 目标日期

        Returns:
            过滤后的帖子列表
        """
        filtered_posts = []

        try:
            with open(jsonl_file, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line:
                        continue

                    try:
                        post_data = json.loads(line)

                        # 检查日期匹配
                        if not self._check_date_match(post_data, target_date):
                            continue

                        # 检查关键词匹配
                        if not self._check_keyword_match(post_data, search_terms):
                            continue

                        # 格式化帖子数据
                        formatted_post = self._format_post_data(post_data)
                        filtered_posts.append(formatted_post)

                    except json.JSONDecodeError as e:
                        if self.debug:
                            print(f"   ⚠️ JSON解析错误 (行{line_num}): {e}")
                        continue

        except Exception as e:
            if self.debug:
                print(f"   ❌ 读取文件失败: {e}")

        return filtered_posts

    def _check_date_match(self, post_data: Dict, target_date: str) -> bool:
        """检查帖子日期是否匹配"""
        try:
            if 'created_utc' in post_data:
                post_date = datetime.fromtimestamp(
                    post_data['created_utc'], tz=timezone.utc
                ).strftime("%Y-%m-%d")
                return post_date == target_date
        except (ValueError, TypeError):
            pass
        return False

    def _check_keyword_match(self, post_data: Dict, search_terms: List[str]) -> bool:
        """检查帖子是否包含搜索关键词"""
        title = post_data.get('title', '').lower()
        content = post_data.get('selftext', '').lower()

        for term in search_terms:
            if term.lower() in title or term.lower() in content:
                return True
        return False

    def _format_post_data(self, post_data: Dict) -> Dict:
        """格式化帖子数据"""
        return {
            'title': post_data.get('title', ''),
            'content': post_data.get('selftext', ''),
            'url': post_data.get('url', ''),
            'upvotes': post_data.get('ups', 0),
            'comments': post_data.get('num_comments', 0),
            'subreddit': post_data.get('subreddit', ''),
            'posted_date': datetime.fromtimestamp(
                post_data.get('created_utc', 0), tz=timezone.utc
            ).strftime("%Y-%m-%d %H:%M:%S") if post_data.get('created_utc') else '',
            'timestamp': post_data.get('created_utc', 0)
        }

    def display_results(self, results: Dict[str, Any]):
        """
        显示获取结果

        Args:
            results: 获取结果字典
        """
        print("\n" + "=" * 60)
        print("📊 Reddit数据获取结果")
        print("=" * 60)

        if not results.get('success'):
            print(f"❌ 获取失败: {results.get('error', 'Unknown error')}")
            return

        # 基本信息
        print(f"🏷️  股票代码: {results['ticker']}")
        print(f"📅 目标日期: {results['target_date']}")
        print(f"📆 查找天数: {results['days_back']}")
        print(f"🔍 搜索关键词: {', '.join(results['search_terms'])}")
        print(f"📝 总帖子数: {results['total_posts']}")
        print(f"⏱️  处理时间: {results['processing_time']:.2f}秒")

        # subreddit分布
        if results['subreddit_stats']:
            print(f"\n📋 Subreddit分布:")
            for subreddit, count in results['subreddit_stats'].items():
                print(f"   {subreddit}: {count} 个帖子")

        # 显示帖子详情
        posts = results.get('posts', [])
        if posts:
            print(f"\n📖 帖子详情 (显示前5个):")
            print("-" * 60)

            for i, post in enumerate(posts[:5], 1):
                print(f"\n{i}. 【{post['subreddit']}】{post['title']}")
                print(f"   ⬆️  {post['upvotes']} 赞 | 💬 {post['comments']} 评论")
                print(f"   📅 {post['posted_date']}")

                if post['content']:
                    content_preview = post['content'][:200]
                    if len(post['content']) > 200:
                        content_preview += "..."
                    print(f"   📄 {content_preview}")

                print(f"   🔗 {post['url']}")
        else:
            print("\n📖 未找到相关帖子")

    def save_results(self, results: Dict[str, Any], output_dir: Optional[Path] = None) -> Path:
        """
        保存获取结果到文件

        Args:
            results: 获取结果
            output_dir: 输出目录

        Returns:
            保存的文件路径
        """
        if output_dir is None:
            output_dir = self.data_dir / "reddit_analysis"

        output_dir.mkdir(parents=True, exist_ok=True)

        # 生成文件名
        ticker = results.get('ticker', 'unknown')
        date = results.get('target_date', 'unknown')
        timestamp = datetime.now().strftime("%H%M%S")

        filename = f"{ticker}-reddit-{date}-{timestamp}.json"
        filepath = output_dir / filename

        # 保存JSON文件
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)

        # 保存可读的文本文件
        txt_filename = f"{ticker}-reddit-{date}-{timestamp}.txt"
        txt_filepath = output_dir / txt_filename

        with open(txt_filepath, 'w', encoding='utf-8') as f:
            f.write(f"Reddit数据获取结果 - {ticker} ({date})\n")
            f.write("=" * 50 + "\n\n")

            f.write(f"股票代码: {results.get('ticker', 'N/A')}\n")
            f.write(f"目标日期: {results.get('target_date', 'N/A')}\n")
            f.write(f"查找天数: {results.get('days_back', 'N/A')}\n")
            f.write(f"搜索关键词: {', '.join(results.get('search_terms', []))}\n")
            f.write(f"总帖子数: {results.get('total_posts', 0)}\n")
            f.write(f"处理时间: {results.get('processing_time', 0):.2f}秒\n\n")

            # 写入帖子详情
            posts = results.get('posts', [])
            if posts:
                f.write("帖子详情:\n")
                f.write("-" * 40 + "\n\n")

                for i, post in enumerate(posts, 1):
                    f.write(f"{i}. 【{post['subreddit']}】{post['title']}\n")
                    f.write(f"   赞数: {post['upvotes']} | 评论: {post['comments']}\n")
                    f.write(f"   时间: {post['posted_date']}\n")

                    if post['content']:
                        f.write(f"   内容: {post['content']}\n")

                    f.write(f"   链接: {post['url']}\n\n")

        if self.debug:
            print(f"💾 结果已保存到: {filepath}")
            print(f"💾 文本文件: {txt_filepath}")

        return filepath

    def get_sentiment_summary(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成情绪摘要

        Args:
            results: 获取结果

        Returns:
            情绪摘要字典
        """
        posts = results.get('posts', [])
        if not posts:
            return {
                'total_posts': 0,
                'positive_posts': 0,
                'negative_posts': 0,
                'neutral_posts': 0,
                'overall_sentiment': 'neutral',
                'total_upvotes': 0,
                'average_upvotes': 0
            }

        # 简单的情绪分析（基于关键词）
        positive_keywords = [
            'bullish', 'buy', 'moon', 'rocket', 'strong', 'good', 'great',
            'excellent', 'positive', 'up', 'rise', 'gain', 'profit',
            '看涨', '买入', '上涨', '利好', '强势', '看好'
        ]

        negative_keywords = [
            'bearish', 'sell', 'short', 'weak', 'bad', 'terrible',
            'negative', 'down', 'fall', 'loss', 'crash',
            '看跌', '卖出', '下跌', '利空', '弱势', '看空'
        ]

        positive_count = 0
        negative_count = 0
        total_upvotes = 0

        for post in posts:
            text = (post['title'] + ' ' + post['content']).lower()
            upvotes = post.get('upvotes', 0)
            total_upvotes += upvotes

            # 计算正面和负面关键词
            pos_score = sum(1 for keyword in positive_keywords if keyword in text)
            neg_score = sum(1 for keyword in negative_keywords if keyword in text)

            if pos_score > neg_score:
                positive_count += 1
            elif neg_score > pos_score:
                negative_count += 1

        # 确定整体情绪
        if positive_count > negative_count:
            overall_sentiment = 'positive'
        elif negative_count > positive_count:
            overall_sentiment = 'negative'
        else:
            overall_sentiment = 'neutral'

        return {
            'total_posts': len(posts),
            'positive_posts': positive_count,
            'negative_posts': negative_count,
            'neutral_posts': len(posts) - positive_count - negative_count,
            'overall_sentiment': overall_sentiment,
            'total_upvotes': total_upvotes,
            'average_upvotes': total_upvotes / len(posts) if posts else 0
        }


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="Reddit社交媒体数据获取脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 获取AAPL在指定日期的Reddit讨论
  python scripts/reddit_data_fetcher.py --ticker AAPL --date 2024-05-20

  # 获取TSLA过去3天的讨论
  python scripts/reddit_data_fetcher.py --ticker TSLA --date 2024-05-20 --days 3

  # 获取NVDA讨论并保存结果
  python scripts/reddit_data_fetcher.py --ticker NVDA --date 2024-05-20 --limit 10 --save

  # 静默模式获取数据
  python scripts/reddit_data_fetcher.py --ticker MSFT --date 2024-05-20 --quiet
        """
    )

    parser.add_argument(
        '--ticker', '-t',
        required=True,
        help='股票代码 (例如: AAPL, TSLA, NVDA)'
    )

    parser.add_argument(
        '--date', '-d',
        required=True,
        help='目标日期，格式: YYYY-MM-DD (例如: 2024-05-20)'
    )

    parser.add_argument(
        '--days',
        type=int,
        default=1,
        help='向前查找的天数 (默认: 1)'
    )

    parser.add_argument(
        '--limit', '-l',
        type=int,
        default=5,
        help='每个subreddit的最大帖子数 (默认: 5)'
    )

    parser.add_argument(
        '--data-dir',
        type=Path,
        help='数据目录路径 (默认: data/)'
    )

    parser.add_argument(
        '--output', '-o',
        type=Path,
        help='输出目录 (默认: data/reddit_analysis)'
    )

    parser.add_argument(
        '--save',
        action='store_true',
        help='保存结果到文件'
    )

    parser.add_argument(
        '--quiet', '-q',
        action='store_true',
        help='静默模式，减少输出信息'
    )

    parser.add_argument(
        '--sentiment',
        action='store_true',
        help='显示情绪分析摘要'
    )

    args = parser.parse_args()

    # 验证日期格式
    try:
        datetime.strptime(args.date, '%Y-%m-%d')
    except ValueError:
        print("❌ 日期格式错误，请使用 YYYY-MM-DD 格式")
        sys.exit(1)

    # 验证天数
    if args.days < 1:
        print("❌ 查找天数必须大于0")
        sys.exit(1)

    # 初始化数据获取器
    fetcher = RedditDataFetcher(
        data_dir=args.data_dir,
        debug=not args.quiet
    )

    if not args.quiet:
        print("🚀 Reddit社交媒体数据获取脚本")
        print("=" * 50)
        print(f"📊 股票代码: {args.ticker}")
        print(f"📅 目标日期: {args.date}")
        print(f"📆 查找天数: {args.days}")
        print(f"📊 每个subreddit限制: {args.limit}")

    try:
        # 获取Reddit数据
        results = fetcher.fetch_reddit_posts(
            ticker=args.ticker,
            target_date=args.date,
            days_back=args.days,
            max_posts_per_subreddit=args.limit
        )

        # 显示结果
        if not args.quiet:
            fetcher.display_results(results)

        # 显示情绪分析
        if args.sentiment and results.get('success'):
            sentiment_summary = fetcher.get_sentiment_summary(results)

            print(f"\n📈 情绪分析摘要:")
            print("-" * 30)
            print(f"总帖子数: {sentiment_summary['total_posts']}")

            if sentiment_summary['total_posts'] > 0:
                print(f"正面帖子: {sentiment_summary['positive_posts']}")
                print(f"负面帖子: {sentiment_summary['negative_posts']}")
                print(f"中性帖子: {sentiment_summary['neutral_posts']}")
                print(f"整体情绪: {sentiment_summary['overall_sentiment']}")
                print(f"总赞数: {sentiment_summary['total_upvotes']}")
                print(f"平均赞数: {sentiment_summary['average_upvotes']:.1f}")
            else:
                print("⚠️ 没有找到帖子，无法进行情绪分析")

        # 保存结果
        if args.save and results.get('success'):
            filepath = fetcher.save_results(results, args.output)
            if not args.quiet:
                print(f"\n💾 结果已保存到: {filepath}")

        # 总结
        if results.get('success'):
            total_posts = results.get('total_posts', 0)
            if not args.quiet:
                print(f"\n🎯 获取完成! 共找到 {total_posts} 个相关帖子")
        else:
            print(f"\n❌ 获取失败: {results.get('error', 'Unknown error')}")
            sys.exit(1)

    except KeyboardInterrupt:
        print("\n⚠️ 用户中断执行")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 执行出错: {e}")
        if not args.quiet:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
