#!/usr/bin/env python3
"""
真实Reddit股票社交媒体数据获取脚本

这个脚本使用Reddit API获取真实的股票相关社交媒体数据，支持：
1. 多个subreddit同时搜索
2. 智能股票关键词匹配
3. 时间范围过滤
4. 数据质量评估
5. 情绪分析
6. 结果保存和展示

使用方法：
python test_reddit.py --ticker AAPL --days 7 --limit 20
python test_reddit.py --ticker TSLA --days 3 --save
"""

import praw
import argparse
import json
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional
import re
from collections import defaultdict


class RealRedditDataFetcher:
    """真实Reddit数据获取器"""

    def __init__(self, debug: bool = True):
        """
        初始化Reddit数据获取器

        Args:
            debug: 是否显示详细调试信息
        """
        self.debug = debug

        # 初始化Reddit API客户端
        try:
            self.reddit = praw.Reddit(
                client_id="vyg0MLjDgpqYQRNHaJaJEQ",
                client_secret="hy412_1dYvB0xXKe5tUWYCTSeFZpGQ",
                user_agent="ai_fund_hedge by /u/Available_Neck_1936"
            )

            # 测试API连接
            self.reddit.user.me()
            if self.debug:
                print("✅ Reddit API连接成功")

        except Exception as e:
            if self.debug:
                print(f"❌ Reddit API连接失败: {e}")
            self.reddit = None

        # 支持的subreddit列表
        self.target_subreddits = [
            "stocks", "investing", "wallstreetbets", "SecurityAnalysis",
            "ValueInvesting", "StockMarket", "pennystocks", "options",
            "financialindependence", "dividends"
        ]

        # 股票代码到公司名称的映射
        self.ticker_to_company = {
            "AAPL": ["Apple", "AAPL"],
            "TSLA": ["Tesla", "TSLA"],
            "NVDA": ["Nvidia", "NVDA"],
            "META": ["Meta", "Facebook", "META"],
            "GOOGL": ["Google", "Alphabet", "GOOGL"],
            "MSFT": ["Microsoft", "MSFT"],
            "AMZN": ["Amazon", "AMZN"],
            "TSM": ["Taiwan Semiconductor", "TSMC", "TSM"],
            "JPM": ["JPMorgan", "JP Morgan", "JPM"],
            "V": ["Visa", "V"],
            "WMT": ["Walmart", "WMT"],
            "AMD": ["AMD"],
            "INTC": ["Intel", "INTC"],
            "NFLX": ["Netflix", "NFLX"],
            "PYPL": ["PayPal", "PYPL"],
            "PLTR": ["Palantir", "PLTR"],
            "MU": ["Micron", "MU"],
            "ZM": ["Zoom", "ZM"],
            "CSCO": ["Cisco", "CSCO"],
            "ORCL": ["Oracle", "ORCL"],
            "ADBE": ["Adobe", "ADBE"],
            "CRM": ["Salesforce", "CRM"],
            "SHOP": ["Shopify", "SHOP"],
            "SQ": ["Block", "Square", "SQ"],
            "SPOT": ["Spotify", "SPOT"],
            "SNAP": ["Snap", "Snapchat", "SNAP"],
            "X": ["Twitter", "X"],
            "BABA": ["Alibaba", "BABA"],
            "QCOM": ["Qualcomm", "QCOM"],
            "AVGO": ["Broadcom", "AVGO"],
            "ASML": ["ASML"],
            "TWLO": ["Twilio", "TWLO"],
            "TEAM": ["Atlassian", "TEAM"]
        }

    def get_search_terms(self, ticker: str) -> List[str]:
        """
        获取股票的搜索关键词

        Args:
            ticker: 股票代码

        Returns:
            搜索关键词列表
        """
        ticker = ticker.upper()
        if ticker in self.ticker_to_company:
            return self.ticker_to_company[ticker]
        else:
            return [ticker]

    def fetch_reddit_posts(
        self,
        ticker: str,
        days_back: int = 7,
        posts_per_subreddit: int = 10
    ) -> Dict[str, Any]:
        """
        获取真实的Reddit帖子数据

        Args:
            ticker: 股票代码
            days_back: 向前查找的天数
            posts_per_subreddit: 每个subreddit的最大帖子数

        Returns:
            包含获取结果的字典
        """
        if not self.reddit:
            return {
                'success': False,
                'error': 'Reddit API未初始化',
                'ticker': ticker
            }

        if self.debug:
            print(f"\n🔍 获取 {ticker} 的真实Reddit数据")
            print("=" * 50)

        start_time = time.time()

        # 获取搜索关键词
        search_terms = self.get_search_terms(ticker)

        if self.debug:
            print(f"🏷️  股票代码: {ticker}")
            print(f"📆 查找天数: {days_back}")
            print(f"🔍 搜索关键词: {search_terms}")
            print(f"📊 每个subreddit限制: {posts_per_subreddit}")

        # 计算时间范围
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days_back)

        if self.debug:
            print(f"📅 时间范围: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")

        all_posts = []
        subreddit_stats = {}
        failed_subreddits = []

        # 遍历每个subreddit
        for subreddit_name in self.target_subreddits:
            if self.debug:
                print(f"\n📂 处理subreddit: {subreddit_name}")

            try:
                subreddit_posts = self._fetch_from_subreddit(
                    subreddit_name, search_terms, start_date, posts_per_subreddit
                )

                if subreddit_posts:
                    all_posts.extend(subreddit_posts)
                    subreddit_stats[subreddit_name] = len(subreddit_posts)

                    if self.debug:
                        print(f"   ✅ 找到 {len(subreddit_posts)} 个相关帖子")
                else:
                    if self.debug:
                        print(f"   ⚠️ 未找到相关帖子")

                # 添加延迟避免API限制
                time.sleep(1)

            except Exception as e:
                failed_subreddits.append(subreddit_name)
                if self.debug:
                    print(f"   ❌ 获取失败: {e}")
                continue

        end_time = time.time()

        # 按分数排序（结合赞数和评论数）
        all_posts.sort(key=lambda x: x.get('score', 0) + x.get('num_comments', 0) * 0.1, reverse=True)

        # 统计结果
        total_posts = len(all_posts)
        processing_time = end_time - start_time

        if self.debug:
            print(f"\n📊 获取结果统计:")
            print(f"   总帖子数: {total_posts}")
            print(f"   处理时间: {processing_time:.2f}秒")
            print(f"   成功的subreddit: {len(subreddit_stats)}")
            print(f"   失败的subreddit: {len(failed_subreddits)}")
            if subreddit_stats:
                print(f"   subreddit分布: {subreddit_stats}")

        return {
            'success': True,
            'ticker': ticker,
            'days_back': days_back,
            'search_terms': search_terms,
            'posts': all_posts,
            'total_posts': total_posts,
            'subreddit_stats': subreddit_stats,
            'failed_subreddits': failed_subreddits,
            'processing_time': processing_time,
            'time_range': {
                'start': start_date.isoformat(),
                'end': end_date.isoformat()
            },
            'timestamp': datetime.now().isoformat()
        }

    def _fetch_from_subreddit(
        self,
        subreddit_name: str,
        search_terms: List[str],
        start_date: datetime,
        limit: int
    ) -> List[Dict]:
        """
        从指定subreddit获取帖子

        Args:
            subreddit_name: subreddit名称
            search_terms: 搜索关键词列表
            start_date: 开始日期
            limit: 帖子数量限制

        Returns:
            帖子列表
        """
        posts = []

        try:
            subreddit = self.reddit.subreddit(subreddit_name)

            # 为每个搜索关键词进行搜索
            for term in search_terms:
                if self.debug:
                    print(f"   🔍 搜索关键词: {term}")

                # 搜索帖子
                search_results = subreddit.search(
                    term,
                    time_filter="week",  # 过去一周
                    sort="relevance",    # 按相关性排序
                    limit=limit // len(search_terms) + 1  # 平均分配限制
                )

                for submission in search_results:
                    # 检查帖子时间
                    post_time = datetime.fromtimestamp(submission.created_utc)
                    if post_time < start_date:
                        continue

                    # 检查是否真的相关（避免误匹配）
                    if not self._is_relevant_post(submission, search_terms):
                        continue

                    # 格式化帖子数据
                    post_data = {
                        'id': submission.id,
                        'title': submission.title,
                        'content': submission.selftext,
                        'url': submission.url,
                        'permalink': f"https://reddit.com{submission.permalink}",
                        'score': submission.score,
                        'upvote_ratio': submission.upvote_ratio,
                        'num_comments': submission.num_comments,
                        'created_utc': submission.created_utc,
                        'created_date': post_time.strftime("%Y-%m-%d %H:%M:%S"),
                        'author': str(submission.author) if submission.author else '[deleted]',
                        'subreddit': subreddit_name,
                        'search_term': term
                    }

                    posts.append(post_data)

                    if len(posts) >= limit:
                        break

                if len(posts) >= limit:
                    break

                # 添加小延迟
                time.sleep(0.5)

        except Exception as e:
            if self.debug:
                print(f"   ❌ subreddit {subreddit_name} 搜索失败: {e}")
            return []

        # 去重（基于帖子ID）
        seen_ids = set()
        unique_posts = []
        for post in posts:
            if post['id'] not in seen_ids:
                seen_ids.add(post['id'])
                unique_posts.append(post)

        return unique_posts[:limit]

    def _is_relevant_post(self, submission, search_terms: List[str]) -> bool:
        """
        检查帖子是否真的相关

        Args:
            submission: Reddit帖子对象
            search_terms: 搜索关键词列表

        Returns:
            是否相关
        """
        title = submission.title.lower()
        content = submission.selftext.lower()

        # 检查标题和内容中是否包含搜索关键词
        for term in search_terms:
            term_lower = term.lower()
            if term_lower in title or term_lower in content:
                return True

        return False

    def analyze_sentiment(self, posts: List[Dict]) -> Dict[str, Any]:
        """
        分析帖子情绪

        Args:
            posts: 帖子列表

        Returns:
            情绪分析结果
        """
        if not posts:
            return {
                'total_posts': 0,
                'positive_posts': 0,
                'negative_posts': 0,
                'neutral_posts': 0,
                'overall_sentiment': 'neutral',
                'average_score': 0,
                'total_score': 0
            }

        # 情绪关键词
        positive_keywords = [
            'bullish', 'buy', 'moon', 'rocket', 'strong', 'good', 'great',
            'excellent', 'positive', 'up', 'rise', 'gain', 'profit', 'bull',
            'long', 'hold', 'diamond hands', 'to the moon', 'calls'
        ]

        negative_keywords = [
            'bearish', 'sell', 'short', 'weak', 'bad', 'terrible',
            'negative', 'down', 'fall', 'loss', 'crash', 'bear',
            'puts', 'dump', 'overvalued', 'bubble'
        ]

        positive_count = 0
        negative_count = 0
        total_score = 0

        for post in posts:
            text = (post['title'] + ' ' + post['content']).lower()
            score = post.get('score', 0)
            total_score += score

            # 计算正面和负面关键词
            pos_score = sum(1 for keyword in positive_keywords if keyword in text)
            neg_score = sum(1 for keyword in negative_keywords if keyword in text)

            if pos_score > neg_score:
                positive_count += 1
            elif neg_score > pos_score:
                negative_count += 1

        neutral_count = len(posts) - positive_count - negative_count

        # 确定整体情绪
        if positive_count > negative_count:
            overall_sentiment = 'positive'
        elif negative_count > positive_count:
            overall_sentiment = 'negative'
        else:
            overall_sentiment = 'neutral'

        return {
            'total_posts': len(posts),
            'positive_posts': positive_count,
            'negative_posts': negative_count,
            'neutral_posts': neutral_count,
            'overall_sentiment': overall_sentiment,
            'average_score': total_score / len(posts) if posts else 0,
            'total_score': total_score
        }

    def display_results(self, results: Dict[str, Any]):
        """
        显示获取结果

        Args:
            results: 获取结果字典
        """
        print("\n" + "=" * 60)
        print("📊 真实Reddit数据获取结果")
        print("=" * 60)

        if not results.get('success'):
            print(f"❌ 获取失败: {results.get('error', 'Unknown error')}")
            return

        # 基本信息
        print(f"🏷️  股票代码: {results['ticker']}")
        print(f"📆 查找天数: {results['days_back']}")
        print(f"🔍 搜索关键词: {', '.join(results['search_terms'])}")
        print(f"📝 总帖子数: {results['total_posts']}")
        print(f"⏱️  处理时间: {results['processing_time']:.2f}秒")

        # 时间范围
        time_range = results.get('time_range', {})
        if time_range:
            start_time = time_range.get('start', '')[:10]  # 只显示日期部分
            end_time = time_range.get('end', '')[:10]
            print(f"📅 时间范围: {start_time} 到 {end_time}")

        # subreddit分布
        subreddit_stats = results.get('subreddit_stats', {})
        if subreddit_stats:
            print(f"\n📋 Subreddit分布:")
            for subreddit, count in sorted(subreddit_stats.items(), key=lambda x: x[1], reverse=True):
                print(f"   {subreddit}: {count} 个帖子")

        # 失败的subreddit
        failed_subreddits = results.get('failed_subreddits', [])
        if failed_subreddits:
            print(f"\n⚠️ 失败的subreddit: {', '.join(failed_subreddits)}")

        # 显示帖子详情
        posts = results.get('posts', [])
        if posts:
            print(f"\n📖 帖子详情 (显示前5个最热门的):")
            print("-" * 60)

            for i, post in enumerate(posts[:5], 1):
                print(f"\n{i}. 【{post['subreddit']}】{post['title']}")
                print(f"   ⬆️  {post['score']} 分 | 💬 {post['num_comments']} 评论 | 👍 {post.get('upvote_ratio', 0):.0%} 赞成率")
                print(f"   📅 {post['created_date']} | 👤 {post['author']}")

                if post['content'] and len(post['content'].strip()) > 0:
                    content_preview = post['content'][:200].replace('\n', ' ')
                    if len(post['content']) > 200:
                        content_preview += "..."
                    print(f"   📄 {content_preview}")

                print(f"   🔗 {post['permalink']}")
        else:
            print("\n📖 未找到相关帖子")

    def save_results(self, results: Dict[str, Any], output_file: Optional[str] = None) -> str:
        """
        保存获取结果到文件

        Args:
            results: 获取结果
            output_file: 输出文件路径

        Returns:
            保存的文件路径
        """
        if output_file is None:
            ticker = results.get('ticker', 'unknown')
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"reddit_data_{ticker}_{timestamp}.json"

        # 保存JSON文件
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)

        # 保存可读的文本文件
        txt_file = output_file.replace('.json', '.txt')
        with open(txt_file, 'w', encoding='utf-8') as f:
            f.write(f"Reddit真实数据获取结果 - {results.get('ticker', 'N/A')}\n")
            f.write("=" * 50 + "\n\n")

            f.write(f"股票代码: {results.get('ticker', 'N/A')}\n")
            f.write(f"查找天数: {results.get('days_back', 'N/A')}\n")
            f.write(f"搜索关键词: {', '.join(results.get('search_terms', []))}\n")
            f.write(f"总帖子数: {results.get('total_posts', 0)}\n")
            f.write(f"处理时间: {results.get('processing_time', 0):.2f}秒\n\n")

            # 写入帖子详情
            posts = results.get('posts', [])
            if posts:
                f.write("帖子详情:\n")
                f.write("-" * 40 + "\n\n")

                for i, post in enumerate(posts, 1):
                    f.write(f"{i}. 【{post['subreddit']}】{post['title']}\n")
                    f.write(f"   分数: {post['score']} | 评论: {post['num_comments']} | 赞成率: {post.get('upvote_ratio', 0):.0%}\n")
                    f.write(f"   时间: {post['created_date']} | 作者: {post['author']}\n")

                    if post['content']:
                        f.write(f"   内容: {post['content']}\n")

                    f.write(f"   链接: {post['permalink']}\n\n")

        if self.debug:
            print(f"💾 结果已保存到: {output_file}")
            print(f"💾 文本文件: {txt_file}")

        return output_file


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="真实Reddit股票社交媒体数据获取脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 获取AAPL过去7天的Reddit讨论
  python test_reddit.py --ticker AAPL --days 7 --limit 20

  # 获取TSLA过去3天的讨论并保存
  python test_reddit.py --ticker TSLA --days 3 --save

  # 获取NVDA讨论并进行情绪分析
  python test_reddit.py --ticker NVDA --days 5 --sentiment
        """
    )

    parser.add_argument(
        '--ticker', '-t',
        required=True,
        help='股票代码 (例如: AAPL, TSLA, NVDA)'
    )

    parser.add_argument(
        '--days', '-d',
        type=int,
        default=7,
        help='向前查找的天数 (默认: 7)'
    )

    parser.add_argument(
        '--limit', '-l',
        type=int,
        default=10,
        help='每个subreddit的最大帖子数 (默认: 10)'
    )

    parser.add_argument(
        '--save',
        action='store_true',
        help='保存结果到文件'
    )

    parser.add_argument(
        '--output', '-o',
        help='输出文件路径'
    )

    parser.add_argument(
        '--sentiment',
        action='store_true',
        help='显示情绪分析'
    )

    parser.add_argument(
        '--quiet', '-q',
        action='store_true',
        help='静默模式，减少输出信息'
    )

    args = parser.parse_args()

    # 验证天数
    if args.days < 1:
        print("❌ 查找天数必须大于0")
        return

    # 初始化数据获取器
    fetcher = RealRedditDataFetcher(debug=not args.quiet)

    if not args.quiet:
        print("🚀 真实Reddit股票社交媒体数据获取脚本")
        print("=" * 50)
        print(f"📊 股票代码: {args.ticker}")
        print(f"📆 查找天数: {args.days}")
        print(f"📊 每个subreddit限制: {args.limit}")

    try:
        # 获取Reddit数据
        results = fetcher.fetch_reddit_posts(
            ticker=args.ticker,
            days_back=args.days,
            posts_per_subreddit=args.limit
        )

        # 显示结果
        if not args.quiet:
            fetcher.display_results(results)

        # 显示情绪分析
        if args.sentiment and results.get('success'):
            posts = results.get('posts', [])
            sentiment_summary = fetcher.analyze_sentiment(posts)

            print(f"\n📈 情绪分析摘要:")
            print("-" * 30)
            print(f"总帖子数: {sentiment_summary['total_posts']}")

            if sentiment_summary['total_posts'] > 0:
                print(f"正面帖子: {sentiment_summary['positive_posts']}")
                print(f"负面帖子: {sentiment_summary['negative_posts']}")
                print(f"中性帖子: {sentiment_summary['neutral_posts']}")
                print(f"整体情绪: {sentiment_summary['overall_sentiment']}")
                print(f"平均分数: {sentiment_summary['average_score']:.1f}")
                print(f"总分数: {sentiment_summary['total_score']}")
            else:
                print("⚠️ 没有找到帖子，无法进行情绪分析")

        # 保存结果
        if args.save and results.get('success'):
            filepath = fetcher.save_results(results, args.output)
            if not args.quiet:
                print(f"\n💾 结果已保存到: {filepath}")

        # 总结
        if results.get('success'):
            total_posts = results.get('total_posts', 0)
            if not args.quiet:
                print(f"\n🎯 获取完成! 共找到 {total_posts} 个真实Reddit帖子")
        else:
            print(f"\n❌ 获取失败: {results.get('error', 'Unknown error')}")

    except KeyboardInterrupt:
        print("\n⚠️ 用户中断执行")
    except Exception as e:
        print(f"\n❌ 执行出错: {e}")
        if not args.quiet:
            import traceback
            traceback.print_exc()


if __name__ == "__main__":
    main()