"""
DeepSeek LLM wrapper for TradingAgents framework.
This module provides a LangChain-compatible interface for DeepSeek models.
"""

import os
from typing import Any, Dict, List, Optional, Union
from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.messages import BaseMessage, AIMessage, HumanMessage, SystemMessage
from langchain_core.outputs import Chat<PERSON>ener<PERSON>, ChatResult
from langchain_core.callbacks import CallbackManager<PERSON><PERSON><PERSON><PERSON><PERSON>
from langchain_core.runnables import Runnable
from langchain_core.prompt_values import ChatPromptValue
from pydantic import Field, PrivateAttr
from openai import OpenAI
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()


class DeepSeekChatModel(BaseChatModel):
    """DeepSeek Chat Model wrapper for LangChain compatibility."""

    model: str = Field(default="deepseek-chat", description="Model name to use")
    temperature: float = Field(default=0.1, description="Temperature for sampling")
    max_tokens: Optional[int] = Field(default=None, description="Maximum tokens to generate")
    api_key: Optional[str] = Field(default=None, description="DeepSeek API key")
    base_url: str = Field(default="https://api.deepseek.com", description="DeepSeek API base URL")

    # Use PrivateAttr for the OpenAI client since it's not serializable
    _client: OpenAI = PrivateAttr()

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # Get API key from environment or parameter
        api_key = kwargs.get('api_key') or self.api_key or os.getenv('DEEPSEEK_API_KEY')
        if not api_key:
            raise ValueError("DEEPSEEK_API_KEY environment variable is required")

        # Initialize OpenAI client with DeepSeek endpoint
        self._client = OpenAI(
            api_key=api_key,
            base_url=self.base_url
        )
    
    @property
    def _llm_type(self) -> str:
        return "deepseek"
    
    def _convert_message_to_dict(self, message: BaseMessage) -> Dict[str, Any]:
        """Convert LangChain message to OpenAI format."""
        if isinstance(message, HumanMessage):
            return {"role": "user", "content": message.content}
        elif isinstance(message, AIMessage):
            return {"role": "assistant", "content": message.content}
        elif isinstance(message, SystemMessage):
            return {"role": "system", "content": message.content}
        else:
            return {"role": "user", "content": str(message.content)}
    
    def _generate(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> ChatResult:
        """Generate chat completion using DeepSeek API."""
        
        # Convert messages to OpenAI format
        openai_messages = [self._convert_message_to_dict(msg) for msg in messages]
        
        # Prepare API call parameters
        params = {
            "model": self.model,
            "messages": openai_messages,
            "temperature": self.temperature,
        }
        
        if self.max_tokens:
            params["max_tokens"] = self.max_tokens
        if stop:
            params["stop"] = stop
        
        # Update with any additional kwargs
        params.update(kwargs)
        
        try:
            # Make API call
            response = self._client.chat.completions.create(**params)
            
            # Extract response content
            content = response.choices[0].message.content
            
            # Create LangChain response
            message = AIMessage(content=content)
            generation = ChatGeneration(message=message)
            
            return ChatResult(generations=[generation])
            
        except Exception as e:
            raise ValueError(f"DeepSeek API call failed: {str(e)}")
    
    async def _agenerate(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> ChatResult:
        """Async version of _generate (not implemented for now)."""
        return self._generate(messages, stop, run_manager, **kwargs)

    def bind_tools(self, tools, **kwargs):
        """Bind tools to the model. DeepSeek doesn't support function calling, so we create a wrapper."""
        # DeepSeek models don't support function calling like OpenAI
        # We'll create a wrapper that simulates tool usage through text generation
        return DeepSeekToolWrapper(self, tools)


class DeepSeekToolWrapper(Runnable):
    """Wrapper that simulates tool usage for DeepSeek models."""

    def __init__(self, llm: DeepSeekChatModel, tools):
        super().__init__()
        self.llm = llm
        self.tools = tools

    def invoke(self, input, config=None, **kwargs):
        """Invoke the LLM without actual tool calling."""
        # For DeepSeek, we'll just use the LLM directly without tool calling
        # The analysis will be based on the model's training data

        # Handle different input types
        if isinstance(input, ChatPromptValue):
            messages = input.messages
        elif isinstance(input, list):
            messages = input
        else:
            messages = [input]

        # Create a modified prompt that mentions the available tools
        if self.tools:
            tool_descriptions = []
            for tool in self.tools:
                tool_descriptions.append(f"- {tool.name}: {tool.description}")

            tool_info = "Available analysis tools:\n" + "\n".join(tool_descriptions)

            # Add tool information to the system message
            if messages and len(messages) > 0:
                # Find the last human message and modify it
                modified_messages = []
                for msg in messages:
                    if hasattr(msg, 'content') and isinstance(msg.content, str):
                        if "system" in str(type(msg)).lower() or msg == messages[0]:
                            # Add tool info to system message
                            modified_content = f"{msg.content}\n\n{tool_info}\n\nPlease provide analysis based on your knowledge and the context provided."
                            modified_msg = type(msg)(content=modified_content)
                            modified_messages.append(modified_msg)
                        else:
                            modified_messages.append(msg)
                    else:
                        modified_messages.append(msg)
                messages = modified_messages

        # Call the underlying LLM
        result = self.llm.invoke(messages, config=config, **kwargs)
        return result


def create_deepseek_llm(model: str = "deepseek-chat", temperature: float = 0.1, **kwargs) -> DeepSeekChatModel:
    """Factory function to create DeepSeek LLM instance."""
    return DeepSeekChatModel(
        model=model,
        temperature=temperature,
        **kwargs
    )


# Available DeepSeek models
DEEPSEEK_MODELS = {
    "deepseek-chat": "DeepSeek Chat - General purpose conversational model",
    "deepseek-coder": "DeepSeek Coder - Specialized for code generation and analysis", 
    "deepseek-reasoner": "DeepSeek Reasoner - Advanced reasoning and problem solving",
}


def get_available_models() -> Dict[str, str]:
    """Get list of available DeepSeek models."""
    return DEEPSEEK_MODELS.copy()
