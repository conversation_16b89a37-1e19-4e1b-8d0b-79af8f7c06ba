#!/usr/bin/env python3
"""
数据获取调试脚本
用于调试和测试在线数据获取的各个组件

使用方法:
python scripts/debug_data_fetch.py --test market --symbol AAPL --date 2024-01-15
"""

import os
import sys
import argparse
from pathlib import Path
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from tradingagents.dataflows import interface
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()


class DataFetchDebugger:
    """数据获取调试器"""
    
    def __init__(self):
        self.project_root = project_root
        print(f"🔧 调试器初始化完成")
        print(f"📁 项目根目录: {self.project_root}")
    
    def check_api_keys(self):
        """检查API密钥配置"""
        print("\n🔑 检查API密钥...")
        
        keys = {
            'DEEPSEEK_API_KEY': os.getenv('DEEPSEEK_API_KEY'),
            'FINNHUB_API_KEY': os.getenv('FINNHUB_API_KEY'),
            'OPENAI_API_KEY': os.getenv('OPENAI_API_KEY')
        }
        
        for key, value in keys.items():
            if value:
                masked_value = value[:8] + "..." + value[-4:] if len(value) > 12 else "***"
                print(f"   ✅ {key}: {masked_value}")
            else:
                print(f"   ❌ {key}: 未配置")
        
        return any(keys.values())
    
    def test_market_data(self, symbol: str, start_date: str, end_date: str):
        """测试市场数据获取"""
        print(f"\n📊 测试市场数据获取: {symbol} ({start_date} ~ {end_date})")
        
        try:
            # 测试价格数据
            print("   测试Yahoo Finance价格数据...")
            price_data = interface.get_YFin_data_online(symbol, start_date, end_date)
            
            if hasattr(price_data, 'shape'):
                print(f"   ✅ 价格数据获取成功: {price_data.shape[0]} 行, {price_data.shape[1]} 列")
                print(f"      列名: {list(price_data.columns)}")
                if not price_data.empty:
                    print(f"      日期范围: {price_data.index[0]} ~ {price_data.index[-1]}")
            else:
                print(f"   ⚠️  价格数据格式异常: {type(price_data)}")
                print(f"      内容: {str(price_data)[:200]}...")
            
            # 测试技术指标
            print("   测试技术指标...")
            indicators = ['rsi_14', 'macd', 'ma_20']
            
            for indicator in indicators:
                try:
                    result = interface.get_stock_stats_indicators_window(
                        symbol, indicator, end_date, 30, True
                    )
                    print(f"     ✅ {indicator}: {result}")
                except Exception as e:
                    print(f"     ❌ {indicator}: {e}")
            
        except Exception as e:
            print(f"   ❌ 市场数据测试失败: {e}")
    
    def test_news_data(self, symbol: str, date: str):
        """测试新闻数据获取"""
        print(f"\n📰 测试新闻数据获取: {symbol} ({date})")
        
        try:
            # 测试全球新闻
            print("   测试全球新闻...")
            global_news = interface.get_global_news_openai(date)
            print(f"   ✅ 全球新闻获取成功: {len(global_news)} 字符")
            print(f"      内容预览: {global_news[:150]}...")
            
            # 检查内容质量
            if self._is_instructional_content(global_news):
                print("   ⚠️  内容似乎是指导性文本，而非实际新闻")
            else:
                print("   ✅ 内容质量良好")
            
            # 测试Google新闻
            print("   测试Google新闻...")
            google_news = interface.get_google_news(symbol, date, 7)
            print(f"   ✅ Google新闻获取成功: {len(str(google_news))} 字符")
            print(f"      内容预览: {str(google_news)[:150]}...")
            
        except Exception as e:
            print(f"   ❌ 新闻数据测试失败: {e}")
    
    def test_social_data(self, symbol: str, date: str):
        """测试社交媒体数据获取"""
        print(f"\n📱 测试社交媒体数据获取: {symbol} ({date})")
        
        try:
            social_news = interface.get_stock_news_openai(symbol, date)
            print(f"   ✅ 社交媒体数据获取成功: {len(social_news)} 字符")
            print(f"      内容预览: {social_news[:150]}...")
            
            # 检查内容质量
            if self._is_instructional_content(social_news):
                print("   ⚠️  内容似乎是指导性文本，而非实际社交媒体数据")
            else:
                print("   ✅ 内容质量良好")
            
        except Exception as e:
            print(f"   ❌ 社交媒体数据测试失败: {e}")
    
    def test_fundamentals_data(self, symbol: str, date: str):
        """测试基本面数据获取"""
        print(f"\n📈 测试基本面数据获取: {symbol} ({date})")
        
        try:
            fundamentals = interface.get_fundamentals_openai(symbol, date)
            print(f"   ✅ 基本面数据获取成功: {len(fundamentals)} 字符")
            print(f"      内容预览: {fundamentals[:150]}...")
            
            # 检查是否包含财务指标
            financial_keywords = ['P/E', 'P/S', 'revenue', 'cash flow', 'dividend', '$']
            found_keywords = [kw for kw in financial_keywords if kw.lower() in fundamentals.lower()]
            
            if found_keywords:
                print(f"   ✅ 包含财务指标: {', '.join(found_keywords)}")
            else:
                print("   ⚠️  未发现明显的财务指标")
            
        except Exception as e:
            print(f"   ❌ 基本面数据测试失败: {e}")
    
    def _is_instructional_content(self, content: str) -> bool:
        """检查是否为指导性内容"""
        instructional_keywords = [
            "search for", "you can use", "here are some", "to find",
            "check these", "look for", "try these", "visit these",
            "use the following", "here's how", "follow these steps"
        ]
        
        content_lower = content.lower()
        return any(keyword in content_lower for keyword in instructional_keywords)
    
    def run_comprehensive_test(self, symbol: str, date: str):
        """运行综合测试"""
        print(f"🧪 开始综合测试: {symbol} ({date})")
        print("=" * 60)
        
        # 检查API密钥
        if not self.check_api_keys():
            print("❌ API密钥检查失败，无法继续测试")
            return
        
        # 计算日期范围
        end_date = datetime.strptime(date, '%Y-%m-%d')
        start_date = end_date - timedelta(days=7)
        start_date_str = start_date.strftime('%Y-%m-%d')
        
        # 测试各个组件
        tests = [
            ("市场数据", lambda: self.test_market_data(symbol, start_date_str, date)),
            ("新闻数据", lambda: self.test_news_data(symbol, date)),
            ("社交媒体数据", lambda: self.test_social_data(symbol, date)),
            ("基本面数据", lambda: self.test_fundamentals_data(symbol, date))
        ]
        
        results = []
        for test_name, test_func in tests:
            try:
                test_func()
                results.append((test_name, "✅ 通过"))
            except Exception as e:
                print(f"   ❌ {test_name}测试异常: {e}")
                results.append((test_name, f"❌ 失败: {e}"))
        
        # 打印测试结果汇总
        print("\n" + "=" * 60)
        print("📊 测试结果汇总:")
        for test_name, result in results:
            print(f"   {test_name}: {result}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='调试TradingAgents数据获取')
    parser.add_argument('--test', choices=['market', 'news', 'social', 'fundamentals', 'all'],
                       default='all', help='要测试的组件')
    parser.add_argument('--symbol', default='AAPL', help='股票代码')
    parser.add_argument('--date', help='测试日期 (YYYY-MM-DD)，默认为昨天')
    parser.add_argument('--start-date', help='开始日期 (仅用于市场数据测试)')
    
    args = parser.parse_args()
    
    # 设置默认日期
    if not args.date:
        args.date = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
    
    if not args.start_date:
        start_dt = datetime.strptime(args.date, '%Y-%m-%d') - timedelta(days=7)
        args.start_date = start_dt.strftime('%Y-%m-%d')
    
    debugger = DataFetchDebugger()
    
    try:
        if args.test == 'all':
            debugger.run_comprehensive_test(args.symbol, args.date)
        elif args.test == 'market':
            debugger.check_api_keys()
            debugger.test_market_data(args.symbol, args.start_date, args.date)
        elif args.test == 'news':
            debugger.check_api_keys()
            debugger.test_news_data(args.symbol, args.date)
        elif args.test == 'social':
            debugger.check_api_keys()
            debugger.test_social_data(args.symbol, args.date)
        elif args.test == 'fundamentals':
            debugger.check_api_keys()
            debugger.test_fundamentals_data(args.symbol, args.date)
        
    except KeyboardInterrupt:
        print("\n⏹️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试执行异常: {e}")


if __name__ == '__main__':
    main()
