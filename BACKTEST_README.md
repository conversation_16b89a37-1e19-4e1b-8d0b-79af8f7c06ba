# TradingAgents 回测框架

## 🎯 概述

TradingAgents回测框架是一个专为TradingAgents项目设计的全面回测系统，支持多模型配置、多代理选择、详细的性能分析和可视化报告生成。该框架完全集成了DeepSeek模型，提供了强大的金融交易策略回测能力。

## ✨ 主要特性

### 🔧 核心功能
- **日期范围选择**: 灵活的时间范围配置和数据可用性验证
- **模型选择**: 支持DeepSeek模型配置，包括推理模型和聊天模型
- **代理选择**: 可选择不同的分析师组合（市场、新闻、基本面、社交媒体）
- **批量处理**: 高效的批量回测执行和进度跟踪
- **错误处理**: 完善的异常处理和恢复机制

### 📊 性能分析
- **收益指标**: 总收益、累计收益、平均收益
- **风险指标**: 波动率、下行波动率、最大回撤
- **风险调整收益**: 夏普比率、索提诺比率、卡尔马比率
- **交易统计**: 胜率、盈亏比、交易次数
- **基准比较**: Alpha、Beta、信息比率、跟踪误差

### 📈 可视化报告
- **累计收益曲线**: 策略vs基准对比
- **回撤分析**: 时间序列回撤曲线
- **决策分布**: 买入/卖出/持有决策统计
- **股票性能**: 各股票表现对比
- **月度热力图**: 按月份的收益分布

### 🧪 实验管理
- **实验生命周期**: 创建、执行、分析、比较、归档
- **配置管理**: JSON/YAML格式配置文件支持
- **结果跟踪**: 完整的实验历史和元数据
- **批量对比**: 多实验结果对比分析

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install pandas numpy matplotlib seaborn tqdm pyyaml
```

### 2. 设置环境变量

```bash
export DEEPSEEK_API_KEY="your_deepseek_api_key"
```

### 3. 快速测试

```bash
# 运行快速测试
python backtest_runner.py --quick-test

# 创建示例配置
python backtest_runner.py --create-configs

# 使用配置文件
python backtest_runner.py --config configs/quick_test_config.json
```

### 4. 验证安装

```bash
python test_backtest_framework.py
```

## 📁 项目结构

```
tradingagents/backtesting/
├── __init__.py              # 模块初始化
├── config.py                # 配置管理
├── engine.py                # 回测执行引擎
├── performance.py           # 性能分析
├── visualization.py         # 结果可视化
└── experiment.py            # 实验管理

configs/                     # 配置文件目录
├── quick_test_config.json   # 快速测试配置
├── comprehensive_config.json # 全面分析配置
└── model_comparison_config.yaml # 模型对比配置

examples/                    # 示例代码
├── backtest_example.py      # 使用示例

backtest_runner.py           # 主回测脚本
test_backtest_framework.py   # 测试脚本
BACKTEST_GUIDE.md           # 详细使用指南
```

## 🔧 使用方法

### 命令行界面

```bash
# 基本用法
python backtest_runner.py --config my_config.json

# 快速测试
python backtest_runner.py --quick-test

# 列出所有实验
python backtest_runner.py --list-experiments

# 创建示例配置
python backtest_runner.py --create-configs

# 不生成报告（仅执行回测）
python backtest_runner.py --config my_config.json --no-reports
```

### 编程接口

```python
from tradingagents.backtesting import (
    BacktestConfig, BacktestEngine, ExperimentManager
)

# 创建配置
config = BacktestConfig(
    experiment_name="my_backtest",
    start_date="2024-01-01",
    end_date="2024-03-31",
    symbols=["AAPL", "MSFT"],
    model_config=ModelConfig(provider="deepseek"),
    agent_config=AgentConfig(selected_analysts=["market", "news"])
)

# 运行回测
experiment_manager = ExperimentManager()
engine = BacktestEngine(experiment_manager)
result = engine.run_backtest(config)

print(f"回测完成: {result['experiment_id']}")
```

## 📊 配置示例

### 快速测试配置

```json
{
  "experiment_name": "quick_test",
  "start_date": "2024-01-01",
  "end_date": "2024-01-31",
  "symbols": ["AAPL"],
  "model_config": {
    "provider": "deepseek",
    "deep_think_model": "deepseek-reasoner",
    "quick_think_model": "deepseek-chat"
  },
  "agent_config": {
    "selected_analysts": ["market"]
  },
  "online_tools": false
}
```

### 全面分析配置

```json
{
  "experiment_name": "comprehensive_analysis",
  "start_date": "2024-01-01",
  "end_date": "2024-06-30",
  "symbols": ["AAPL", "MSFT", "GOOGL", "AMZN", "TSLA"],
  "agent_config": {
    "selected_analysts": ["market", "social", "news", "fundamentals"],
    "max_debate_rounds": 2
  },
  "online_tools": true
}
```

## 📈 输出结果

每次回测会生成以下输出：

```
experiments/{experiment_id}/
├── config.json              # 实验配置
├── results/
│   ├── summary.json         # 结果摘要
│   ├── detailed_results.json # 详细结果
│   └── trades.csv           # 交易记录
├── reports/
│   ├── performance_report.json # 性能报告
│   └── performance_metrics.csv # 性能指标表
├── charts/
│   ├── cumulative_returns.png  # 累计收益图
│   ├── drawdown.png            # 回撤图
│   ├── decision_distribution.png # 决策分布图
│   ├── symbol_performance.png   # 股票性能图
│   └── monthly_returns.png     # 月度收益热力图
└── logs/                    # 执行日志
```

## 🎯 最佳实践

1. **从简单开始**: 使用快速测试配置验证设置
2. **数据准备**: 确保历史数据文件存在且格式正确
3. **配置管理**: 保存配置文件以便重现实验
4. **结果分析**: 关注风险调整后的收益指标
5. **对比实验**: 使用不同配置进行对比分析

## 🔍 故障排除

### 常见问题

1. **API密钥错误**: 确保设置了`DEEPSEEK_API_KEY`环境变量
2. **数据文件缺失**: 检查`data_dir`配置和数据文件路径
3. **内存不足**: 减少股票数量或缩短时间范围
4. **网络问题**: 使用`online_tools=false`进行离线回测

### 调试模式

```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 运行测试验证
python test_backtest_framework.py
```

## 📚 文档

- [详细使用指南](BACKTEST_GUIDE.md) - 完整的使用说明和API文档
- [示例代码](examples/backtest_example.py) - 各种使用场景的示例
- [配置模板](configs/) - 预定义的配置文件模板

## 🤝 贡献

欢迎贡献代码和改进建议！请遵循以下步骤：

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

## 📄 许可证

本项目采用MIT许可证。

---

**注意**: 这个回测框架专为TradingAgents项目设计，充分利用了DeepSeek模型的能力。在使用前请确保已正确配置API密钥和数据文件。
