#!/usr/bin/env python3
"""
Reddit股票数据批量获取脚本

专门用于获取AAPL、MSFT、NVDA三只股票从2025.01.01-2025.06.25期间的真实Reddit帖子数据。
包含情绪分析，按股票和日期组织文件结构。

功能特点：
1. 批量获取指定时间段的数据
2. 按股票创建独立文件夹
3. 按日期存储帖子数据
4. 每日至少10条帖子
5. 包含详细的情绪分析
6. 支持断点续传
7. 智能重试机制

使用方法：
python reddit_batch_fetcher.py
"""

import praw
import json
import time
import os
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional
import logging
from collections import defaultdict
import re


class RedditBatchFetcher:
    """Reddit批量数据获取器"""
    
    def __init__(self):
        """初始化批量获取器"""
        # 设置日志
        self.setup_logging()
        
        # 初始化Reddit API
        self.setup_reddit_api()
        
        # 目标股票配置
        self.target_stocks = {
            'AAPL': {
                'name': 'Apple',
                'keywords': ['AAPL', 'Apple', 'iPhone', 'iPad', 'Mac', 'iOS'],
                'folder': 'AAPL_Reddit'
            },
            'MSFT': {
                'name': 'Microsoft',
                'keywords': ['MSFT', 'Microsoft', 'Windows', 'Azure', 'Office', 'Xbox'],
                'folder': 'MSFT_Reddit'
            },
            'NVDA': {
                'name': 'Nvidia',
                'keywords': ['NVDA', 'Nvidia', 'GPU', 'GeForce', 'RTX', 'AI chip'],
                'folder': 'NVDA_Reddit'
            }
        }
        
        # 时间范围配置
        self.start_date = datetime(2025, 1, 1)
        self.end_date = datetime(2025, 6, 25)
        
        # 目标subreddit列表
        self.subreddits = [
            'stocks', 'investing', 'wallstreetbets', 'SecurityAnalysis',
            'ValueInvesting', 'StockMarket', 'options', 'dividends',
            'financialindependence', 'pennystocks'
        ]
        
        # 每日最小帖子数
        self.min_posts_per_day = 10
        
        # 情绪分析关键词
        self.sentiment_keywords = {
            'positive': [
                'bullish', 'buy', 'moon', 'rocket', 'strong', 'good', 'great',
                'excellent', 'positive', 'up', 'rise', 'gain', 'profit', 'bull',
                'long', 'hold', 'diamond hands', 'to the moon', 'calls', 'growth',
                'outperform', 'beat', 'exceed', 'solid', 'impressive', 'surge'
            ],
            'negative': [
                'bearish', 'sell', 'short', 'weak', 'bad', 'terrible',
                'negative', 'down', 'fall', 'loss', 'crash', 'bear',
                'puts', 'dump', 'overvalued', 'bubble', 'decline', 'drop',
                'underperform', 'miss', 'disappointing', 'concern', 'risk'
            ]
        }
    
    def setup_logging(self):
        """设置日志系统"""
        # 创建自定义的StreamHandler，强制使用UTF-8编码
        import sys

        # 设置控制台输出编码为UTF-8
        if sys.stdout.encoding != 'utf-8':
            import io
            sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')

        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('reddit_batch_fetcher.log', encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def setup_reddit_api(self):
        """设置Reddit API连接"""
        try:
            self.reddit = praw.Reddit(
                client_id="vyg0MLjDgpqYQRNHaJaJEQ",
                client_secret="hy412_1dYvB0xXKe5tUWYCTSeFZpGQ",
                user_agent="ai_fund_hedge by /u/Available_Neck_1936"
            )
            
            # 测试连接
            self.reddit.user.me()
            self.logger.info("[SUCCESS] Reddit API连接成功")
            
        except Exception as e:
            self.logger.error(f"[ERROR] Reddit API连接失败: {e}")
            raise
    
    def create_folder_structure(self):
        """创建文件夹结构"""
        self.logger.info("[SETUP] 创建文件夹结构...")

        for ticker, config in self.target_stocks.items():
            folder_path = Path(config['folder'])
            folder_path.mkdir(exist_ok=True)
            self.logger.info(f"   创建文件夹: {folder_path}")
    
    def get_date_range(self) -> List[datetime]:
        """获取日期范围列表"""
        dates = []
        current_date = self.start_date
        
        while current_date <= self.end_date:
            dates.append(current_date)
            current_date += timedelta(days=1)
        
        return dates
    
    def calculate_sentiment_score(self, text: str) -> Dict[str, Any]:
        """
        计算情绪得分
        
        Args:
            text: 要分析的文本
            
        Returns:
            情绪分析结果
        """
        text_lower = text.lower()
        
        # 计算正面和负面关键词数量
        positive_count = sum(1 for keyword in self.sentiment_keywords['positive'] 
                           if keyword in text_lower)
        negative_count = sum(1 for keyword in self.sentiment_keywords['negative'] 
                           if keyword in text_lower)
        
        # 计算情绪得分 (-1 到 1)
        total_keywords = positive_count + negative_count
        if total_keywords == 0:
            sentiment_score = 0.0
            sentiment_label = 'neutral'
        else:
            sentiment_score = (positive_count - negative_count) / total_keywords
            if sentiment_score > 0.2:
                sentiment_label = 'positive'
            elif sentiment_score < -0.2:
                sentiment_label = 'negative'
            else:
                sentiment_label = 'neutral'
        
        return {
            'score': round(sentiment_score, 3),
            'label': sentiment_label,
            'positive_keywords': positive_count,
            'negative_keywords': negative_count,
            'total_keywords': total_keywords
        }
    
    def is_relevant_post(self, post, keywords: List[str]) -> bool:
        """
        检查帖子是否与股票相关
        
        Args:
            post: Reddit帖子对象
            keywords: 关键词列表
            
        Returns:
            是否相关
        """
        title = post.title.lower()
        content = post.selftext.lower()
        
        # 检查是否包含任何关键词
        for keyword in keywords:
            if keyword.lower() in title or keyword.lower() in content:
                return True
        
        return False
    
    def fetch_posts_for_date(self, ticker: str, target_date: datetime) -> List[Dict]:
        """
        获取指定日期的帖子
        
        Args:
            ticker: 股票代码
            target_date: 目标日期
            
        Returns:
            帖子列表
        """
        config = self.target_stocks[ticker]
        keywords = config['keywords']
        
        self.logger.info(f"   🔍 获取 {ticker} 在 {target_date.strftime('%Y-%m-%d')} 的帖子...")
        
        all_posts = []
        
        # 遍历每个subreddit
        for subreddit_name in self.subreddits:
            try:
                subreddit = self.reddit.subreddit(subreddit_name)
                
                # 搜索相关帖子
                for keyword in keywords[:2]:  # 限制关键词数量避免API限制
                    try:
                        search_results = subreddit.search(
                            keyword,
                            time_filter="all",
                            sort="new",
                            limit=20
                        )
                        
                        for post in search_results:
                            # 检查帖子日期
                            post_date = datetime.fromtimestamp(post.created_utc)
                            post_date_only = post_date.date()
                            target_date_only = target_date.date()
                            
                            if post_date_only != target_date_only:
                                continue
                            
                            # 检查相关性
                            if not self.is_relevant_post(post, keywords):
                                continue
                            
                            # 计算情绪得分
                            full_text = f"{post.title} {post.selftext}"
                            sentiment = self.calculate_sentiment_score(full_text)
                            
                            # 构建帖子数据
                            post_data = {
                                'id': post.id,
                                'title': post.title,
                                'content': post.selftext,
                                'url': post.url,
                                'permalink': f"https://reddit.com{post.permalink}",
                                'score': post.score,
                                'upvote_ratio': post.upvote_ratio,
                                'num_comments': post.num_comments,
                                'created_utc': post.created_utc,
                                'created_date': post_date.strftime("%Y-%m-%d %H:%M:%S"),
                                'author': str(post.author) if post.author else '[deleted]',
                                'subreddit': subreddit_name,
                                'search_keyword': keyword,
                                'sentiment': sentiment
                            }
                            
                            all_posts.append(post_data)
                        
                        # 添加延迟避免API限制
                        time.sleep(1)
                        
                    except Exception as e:
                        self.logger.warning(f"     ⚠️ 搜索 {keyword} 在 {subreddit_name} 失败: {e}")
                        continue
                
                # 添加subreddit间延迟
                time.sleep(2)
                
            except Exception as e:
                self.logger.warning(f"     ❌ subreddit {subreddit_name} 访问失败: {e}")
                continue
        
        # 去重（基于帖子ID）
        seen_ids = set()
        unique_posts = []
        for post in all_posts:
            if post['id'] not in seen_ids:
                seen_ids.add(post['id'])
                unique_posts.append(post)
        
        # 按分数排序
        unique_posts.sort(key=lambda x: x['score'], reverse=True)
        
        self.logger.info(f"     ✅ 找到 {len(unique_posts)} 个相关帖子")
        
        return unique_posts
    
    def save_daily_posts(self, ticker: str, date: datetime, posts: List[Dict]):
        """
        保存每日帖子数据
        
        Args:
            ticker: 股票代码
            date: 日期
            posts: 帖子列表
        """
        config = self.target_stocks[ticker]
        folder_path = Path(config['folder'])
        
        # 创建日期文件名
        date_str = date.strftime("%Y-%m-%d")
        json_file = folder_path / f"{date_str}.json"
        txt_file = folder_path / f"{date_str}.txt"
        
        # 计算统计信息
        total_posts = len(posts)
        sentiment_stats = {
            'positive': len([p for p in posts if p['sentiment']['label'] == 'positive']),
            'negative': len([p for p in posts if p['sentiment']['label'] == 'negative']),
            'neutral': len([p for p in posts if p['sentiment']['label'] == 'neutral'])
        }
        
        avg_sentiment = sum(p['sentiment']['score'] for p in posts) / len(posts) if posts else 0
        total_score = sum(p['score'] for p in posts)
        avg_score = total_score / len(posts) if posts else 0
        
        # 构建保存数据
        save_data = {
            'ticker': ticker,
            'date': date_str,
            'total_posts': total_posts,
            'posts': posts,
            'statistics': {
                'sentiment_distribution': sentiment_stats,
                'average_sentiment_score': round(avg_sentiment, 3),
                'total_reddit_score': total_score,
                'average_reddit_score': round(avg_score, 1),
                'subreddit_distribution': {}
            },
            'metadata': {
                'fetch_time': datetime.now().isoformat(),
                'min_posts_target': self.min_posts_per_day,
                'target_met': total_posts >= self.min_posts_per_day
            }
        }
        
        # 计算subreddit分布
        subreddit_count = defaultdict(int)
        for post in posts:
            subreddit_count[post['subreddit']] += 1
        save_data['statistics']['subreddit_distribution'] = dict(subreddit_count)
        
        # 保存JSON文件
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(save_data, f, ensure_ascii=False, indent=2)
        
        # 保存可读文本文件
        with open(txt_file, 'w', encoding='utf-8') as f:
            f.write(f"{ticker} Reddit帖子数据 - {date_str}\n")
            f.write("=" * 50 + "\n\n")
            
            f.write(f"总帖子数: {total_posts}\n")
            f.write(f"目标达成: {'✅' if total_posts >= self.min_posts_per_day else '❌'}\n")
            f.write(f"平均情绪得分: {avg_sentiment:.3f}\n")
            f.write(f"平均Reddit得分: {avg_score:.1f}\n\n")
            
            f.write("情绪分布:\n")
            f.write(f"  正面: {sentiment_stats['positive']} 个\n")
            f.write(f"  负面: {sentiment_stats['negative']} 个\n")
            f.write(f"  中性: {sentiment_stats['neutral']} 个\n\n")
            
            f.write("帖子详情:\n")
            f.write("-" * 40 + "\n\n")
            
            for i, post in enumerate(posts, 1):
                f.write(f"{i}. 【{post['subreddit']}】{post['title']}\n")
                f.write(f"   得分: {post['score']} | 评论: {post['num_comments']} | 情绪: {post['sentiment']['label']} ({post['sentiment']['score']})\n")
                f.write(f"   时间: {post['created_date']} | 作者: {post['author']}\n")
                
                if post['content']:
                    content_preview = post['content'][:150].replace('\n', ' ')
                    if len(post['content']) > 150:
                        content_preview += "..."
                    f.write(f"   内容: {content_preview}\n")
                
                f.write(f"   链接: {post['permalink']}\n\n")
        
        self.logger.info(f"     💾 保存到: {json_file}")
        
        return save_data

    def check_existing_data(self, ticker: str, date: datetime) -> bool:
        """
        检查是否已存在数据（支持断点续传）

        Args:
            ticker: 股票代码
            date: 日期

        Returns:
            是否已存在足够的数据
        """
        config = self.target_stocks[ticker]
        folder_path = Path(config['folder'])
        date_str = date.strftime("%Y-%m-%d")
        json_file = folder_path / f"{date_str}.json"

        if not json_file.exists():
            return False

        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # 检查是否达到最小帖子数要求
            total_posts = data.get('total_posts', 0)
            target_met = data.get('metadata', {}).get('target_met', False)

            if total_posts >= self.min_posts_per_day and target_met:
                self.logger.info(f"     ✅ 已存在足够数据 ({total_posts} 个帖子)")
                return True
            else:
                self.logger.info(f"     ⚠️ 数据不足，需要重新获取 (当前: {total_posts} 个)")
                return False

        except Exception as e:
            self.logger.warning(f"     ❌ 读取现有数据失败: {e}")
            return False

    def fetch_stock_data(self, ticker: str):
        """
        获取单个股票的所有数据

        Args:
            ticker: 股票代码
        """
        self.logger.info(f"\n🚀 开始获取 {ticker} 数据...")

        config = self.target_stocks[ticker]
        dates = self.get_date_range()

        total_days = len(dates)
        completed_days = 0
        skipped_days = 0
        failed_days = 0

        for i, date in enumerate(dates, 1):
            self.logger.info(f"\n📅 处理 {ticker} - {date.strftime('%Y-%m-%d')} ({i}/{total_days})")

            try:
                # 检查是否已存在数据
                if self.check_existing_data(ticker, date):
                    skipped_days += 1
                    continue

                # 获取帖子数据
                posts = self.fetch_posts_for_date(ticker, date)

                # 检查是否达到最小要求
                if len(posts) < self.min_posts_per_day:
                    self.logger.warning(f"     ⚠️ 帖子数量不足: {len(posts)}/{self.min_posts_per_day}")

                    # 尝试扩展搜索
                    self.logger.info(f"     🔄 尝试扩展搜索...")
                    additional_posts = self.fetch_additional_posts(ticker, date, self.min_posts_per_day - len(posts))
                    posts.extend(additional_posts)

                    # 去重
                    seen_ids = set()
                    unique_posts = []
                    for post in posts:
                        if post['id'] not in seen_ids:
                            seen_ids.add(post['id'])
                            unique_posts.append(post)
                    posts = unique_posts

                # 保存数据
                save_data = self.save_daily_posts(ticker, date, posts)

                if len(posts) >= self.min_posts_per_day:
                    completed_days += 1
                    self.logger.info(f"     ✅ 完成 ({len(posts)} 个帖子)")
                else:
                    failed_days += 1
                    self.logger.warning(f"     ❌ 未达到目标 ({len(posts)} 个帖子)")

                # 添加延迟避免API限制
                time.sleep(3)

            except Exception as e:
                failed_days += 1
                self.logger.error(f"     ❌ 处理失败: {e}")
                time.sleep(5)  # 错误后等待更长时间
                continue

        # 输出统计信息
        self.logger.info(f"\n📊 {ticker} 数据获取完成:")
        self.logger.info(f"   总天数: {total_days}")
        self.logger.info(f"   完成: {completed_days}")
        self.logger.info(f"   跳过: {skipped_days}")
        self.logger.info(f"   失败: {failed_days}")
        self.logger.info(f"   成功率: {(completed_days + skipped_days) / total_days * 100:.1f}%")

    def fetch_additional_posts(self, ticker: str, target_date: datetime, needed_count: int) -> List[Dict]:
        """
        扩展搜索获取更多帖子

        Args:
            ticker: 股票代码
            target_date: 目标日期
            needed_count: 需要的帖子数量

        Returns:
            额外的帖子列表
        """
        config = self.target_stocks[ticker]
        keywords = config['keywords']

        additional_posts = []

        # 扩展时间范围（前后各1天）
        date_range = [
            target_date - timedelta(days=1),
            target_date,
            target_date + timedelta(days=1)
        ]

        for check_date in date_range:
            if len(additional_posts) >= needed_count:
                break

            for subreddit_name in self.subreddits:
                if len(additional_posts) >= needed_count:
                    break

                try:
                    subreddit = self.reddit.subreddit(subreddit_name)

                    # 使用更多搜索策略
                    search_queries = keywords + [f"{ticker} stock", f"{config['name']} earnings"]

                    for query in search_queries:
                        if len(additional_posts) >= needed_count:
                            break

                        try:
                            search_results = subreddit.search(
                                query,
                                time_filter="month",
                                sort="relevance",
                                limit=10
                            )

                            for post in search_results:
                                if len(additional_posts) >= needed_count:
                                    break

                                # 检查帖子日期
                                post_date = datetime.fromtimestamp(post.created_utc)
                                if abs((post_date.date() - target_date.date()).days) > 1:
                                    continue

                                # 检查相关性
                                if not self.is_relevant_post(post, keywords):
                                    continue

                                # 计算情绪得分
                                full_text = f"{post.title} {post.selftext}"
                                sentiment = self.calculate_sentiment_score(full_text)

                                # 构建帖子数据
                                post_data = {
                                    'id': post.id,
                                    'title': post.title,
                                    'content': post.selftext,
                                    'url': post.url,
                                    'permalink': f"https://reddit.com{post.permalink}",
                                    'score': post.score,
                                    'upvote_ratio': post.upvote_ratio,
                                    'num_comments': post.num_comments,
                                    'created_utc': post.created_utc,
                                    'created_date': post_date.strftime("%Y-%m-%d %H:%M:%S"),
                                    'author': str(post.author) if post.author else '[deleted]',
                                    'subreddit': subreddit_name,
                                    'search_keyword': query,
                                    'sentiment': sentiment,
                                    'extended_search': True
                                }

                                additional_posts.append(post_data)

                            time.sleep(0.5)

                        except Exception as e:
                            continue

                    time.sleep(1)

                except Exception as e:
                    continue

        self.logger.info(f"     🔍 扩展搜索找到 {len(additional_posts)} 个额外帖子")
        return additional_posts

    def generate_summary_report(self):
        """生成总结报告"""
        self.logger.info("\n📋 生成总结报告...")

        summary_data = {
            'generation_time': datetime.now().isoformat(),
            'date_range': {
                'start': self.start_date.strftime('%Y-%m-%d'),
                'end': self.end_date.strftime('%Y-%m-%d'),
                'total_days': (self.end_date - self.start_date).days + 1
            },
            'stocks': {}
        }

        for ticker, config in self.target_stocks.items():
            folder_path = Path(config['folder'])

            if not folder_path.exists():
                continue

            stock_stats = {
                'total_files': 0,
                'total_posts': 0,
                'days_with_data': 0,
                'days_meeting_target': 0,
                'average_sentiment': 0,
                'sentiment_distribution': {'positive': 0, 'negative': 0, 'neutral': 0},
                'top_subreddits': {},
                'date_range_coverage': []
            }

            json_files = list(folder_path.glob("*.json"))
            stock_stats['total_files'] = len(json_files)

            all_sentiments = []
            subreddit_counts = defaultdict(int)

            for json_file in json_files:
                try:
                    with open(json_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)

                    posts_count = data.get('total_posts', 0)
                    stock_stats['total_posts'] += posts_count

                    if posts_count > 0:
                        stock_stats['days_with_data'] += 1

                    if data.get('metadata', {}).get('target_met', False):
                        stock_stats['days_meeting_target'] += 1

                    # 统计情绪
                    sentiment_dist = data.get('statistics', {}).get('sentiment_distribution', {})
                    for sentiment, count in sentiment_dist.items():
                        stock_stats['sentiment_distribution'][sentiment] += count

                    # 收集平均情绪得分
                    avg_sentiment = data.get('statistics', {}).get('average_sentiment_score', 0)
                    if avg_sentiment != 0:
                        all_sentiments.append(avg_sentiment)

                    # 统计subreddit
                    subreddit_dist = data.get('statistics', {}).get('subreddit_distribution', {})
                    for subreddit, count in subreddit_dist.items():
                        subreddit_counts[subreddit] += count

                    # 记录日期
                    stock_stats['date_range_coverage'].append(data.get('date'))

                except Exception as e:
                    self.logger.warning(f"读取文件 {json_file} 失败: {e}")
                    continue

            # 计算平均情绪
            if all_sentiments:
                stock_stats['average_sentiment'] = round(sum(all_sentiments) / len(all_sentiments), 3)

            # 排序subreddit
            stock_stats['top_subreddits'] = dict(sorted(subreddit_counts.items(), key=lambda x: x[1], reverse=True)[:5])

            # 排序日期覆盖
            stock_stats['date_range_coverage'].sort()

            summary_data['stocks'][ticker] = stock_stats

        # 保存总结报告
        summary_file = Path("reddit_batch_summary.json")
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary_data, f, ensure_ascii=False, indent=2)

        # 生成可读报告
        report_file = Path("reddit_batch_report.txt")
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("Reddit股票数据批量获取总结报告\n")
            f.write("=" * 50 + "\n\n")

            f.write(f"生成时间: {summary_data['generation_time']}\n")
            f.write(f"数据时间范围: {summary_data['date_range']['start']} 到 {summary_data['date_range']['end']}\n")
            f.write(f"总天数: {summary_data['date_range']['total_days']}\n\n")

            for ticker, stats in summary_data['stocks'].items():
                f.write(f"{ticker} 统计信息:\n")
                f.write("-" * 30 + "\n")
                f.write(f"  数据文件数: {stats['total_files']}\n")
                f.write(f"  总帖子数: {stats['total_posts']}\n")
                f.write(f"  有数据天数: {stats['days_with_data']}\n")
                f.write(f"  达标天数: {stats['days_meeting_target']}\n")
                f.write(f"  达标率: {stats['days_meeting_target'] / summary_data['date_range']['total_days'] * 100:.1f}%\n")
                f.write(f"  平均情绪得分: {stats['average_sentiment']}\n")

                f.write(f"  情绪分布:\n")
                for sentiment, count in stats['sentiment_distribution'].items():
                    f.write(f"    {sentiment}: {count}\n")

                f.write(f"  主要subreddit:\n")
                for subreddit, count in stats['top_subreddits'].items():
                    f.write(f"    {subreddit}: {count}\n")

                f.write("\n")

        self.logger.info(f"📄 总结报告已保存: {summary_file}")
        self.logger.info(f"📄 可读报告已保存: {report_file}")

    def run_batch_fetch(self):
        """运行批量获取"""
        self.logger.info("🚀 开始Reddit股票数据批量获取")
        self.logger.info(f"📅 时间范围: {self.start_date.strftime('%Y-%m-%d')} 到 {self.end_date.strftime('%Y-%m-%d')}")
        self.logger.info(f"📊 目标股票: {list(self.target_stocks.keys())}")
        self.logger.info(f"📝 每日最小帖子数: {self.min_posts_per_day}")

        start_time = time.time()

        # 创建文件夹结构
        self.create_folder_structure()

        # 逐个处理股票
        for ticker in self.target_stocks.keys():
            try:
                self.fetch_stock_data(ticker)
            except Exception as e:
                self.logger.error(f"❌ {ticker} 处理失败: {e}")
                continue

        # 生成总结报告
        self.generate_summary_report()

        end_time = time.time()
        total_time = end_time - start_time

        self.logger.info(f"\n🎯 批量获取完成!")
        self.logger.info(f"⏱️  总耗时: {total_time / 3600:.2f} 小时")
        self.logger.info(f"📁 数据保存在各股票文件夹中")
        self.logger.info(f"📋 查看总结报告: reddit_batch_report.txt")


def main():
    """主函数"""
    print("🚀 Reddit股票数据批量获取脚本")
    print("=" * 50)
    print("目标股票: AAPL, MSFT, NVDA")
    print("时间范围: 2025-01-01 到 2025-06-25")
    print("每日目标: 至少10条帖子")
    print("包含功能: 情绪分析、按日期存储")
    print("=" * 50)

    try:
        fetcher = RedditBatchFetcher()
        fetcher.run_batch_fetch()

    except KeyboardInterrupt:
        print("\n⚠️ 用户中断执行")
    except Exception as e:
        print(f"\n❌ 执行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
