from tradingagents.graph.trading_graph import TradingAgentsGraph
from tradingagents.default_config import DEFAULT_CONFIG

# Create a custom config using DeepSeek models
config = DEFAULT_CONFIG.copy()
config["llm_provider"] = "deepseek"  # Use DeepSeek instead of OpenAI
config["deep_think_llm"] = "deepseek-reasoner"  # Use DeepSeek reasoning model
config["quick_think_llm"] = "deepseek-chat"     # Use DeepSeek chat model
config["max_debate_rounds"] = 1  # Number of debate rounds
config["online_tools"] = True   # Use online tools for real-time data

# Initialize with custom config
ta = TradingAgentsGraph(debug=True, config=config)

# Run analysis for NVDA on a specific date
_, decision = ta.propagate("NVDA", "2024-05-10")
print(f"Trading Decision: {decision}")

# Memorize mistakes and reflect (uncomment after getting trading results)
# ta.reflect_and_remember(1000)  # parameter is the position returns
