"""
结果可视化模块

生成回测结果的图表和可视化报告。
"""

import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import seaborn as sns
import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 设置样式
sns.set_style("whitegrid")
plt.style.use('seaborn-v0_8')


class ResultVisualizer:
    """结果可视化器"""
    
    def __init__(self, output_dir: str = "charts"):
        """
        初始化可视化器
        
        Args:
            output_dir: 图表输出目录
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 颜色配置
        self.colors = {
            'primary': '#2E86AB',
            'secondary': '#A23B72', 
            'success': '#F18F01',
            'danger': '#C73E1D',
            'neutral': '#6C757D'
        }
    
    def plot_cumulative_returns(self, returns_df: pd.DataFrame, 
                              benchmark_data: Optional[pd.DataFrame] = None,
                              save_path: Optional[str] = None) -> None:
        """
        绘制累计收益曲线
        
        Args:
            returns_df: 收益DataFrame
            benchmark_data: 基准数据
            save_path: 保存路径
        """
        fig, ax = plt.subplots(figsize=(12, 6))
        
        if returns_df.empty:
            ax.text(0.5, 0.5, '暂无数据', ha='center', va='center', transform=ax.transAxes)
            plt.title('累计收益曲线')
            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.show()
            return
        
        # 计算累计收益
        returns_df = returns_df.copy()
        returns_df['date'] = pd.to_datetime(returns_df['date'])
        returns_df = returns_df.sort_values('date')
        returns_df['cumulative_return'] = (1 + returns_df['return_pct']).cumprod() - 1
        
        # 绘制策略收益
        ax.plot(returns_df['date'], returns_df['cumulative_return'] * 100, 
                color=self.colors['primary'], linewidth=2, label='策略收益')
        
        # 绘制基准收益
        if benchmark_data is not None:
            benchmark_data = benchmark_data.copy()
            benchmark_data['Date'] = pd.to_datetime(benchmark_data['Date'])
            benchmark_data = benchmark_data.sort_values('Date')
            benchmark_data['benchmark_return'] = benchmark_data['Close'].pct_change().fillna(0)
            benchmark_data['benchmark_cumulative'] = (1 + benchmark_data['benchmark_return']).cumprod() - 1
            
            ax.plot(benchmark_data['Date'], benchmark_data['benchmark_cumulative'] * 100,
                   color=self.colors['secondary'], linewidth=2, label='基准收益', alpha=0.7)
        
        ax.set_title('累计收益曲线', fontsize=16, fontweight='bold')
        ax.set_xlabel('日期', fontsize=12)
        ax.set_ylabel('累计收益率 (%)', fontsize=12)
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 格式化x轴
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        ax.xaxis.set_major_locator(mdates.MonthLocator(interval=1))
        plt.xticks(rotation=45)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
    
    def plot_drawdown(self, returns_df: pd.DataFrame, save_path: Optional[str] = None) -> None:
        """
        绘制回撤曲线
        
        Args:
            returns_df: 收益DataFrame
            save_path: 保存路径
        """
        fig, ax = plt.subplots(figsize=(12, 6))
        
        if returns_df.empty:
            ax.text(0.5, 0.5, '暂无数据', ha='center', va='center', transform=ax.transAxes)
            plt.title('回撤曲线')
            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.show()
            return
        
        # 计算回撤
        returns_df = returns_df.copy()
        returns_df['date'] = pd.to_datetime(returns_df['date'])
        returns_df = returns_df.sort_values('date')
        
        cumulative_returns = (1 + returns_df['return_pct']).cumprod()
        running_max = cumulative_returns.expanding().max()
        drawdown = (cumulative_returns - running_max) / running_max * 100
        
        # 绘制回撤
        ax.fill_between(returns_df['date'], drawdown, 0, 
                       color=self.colors['danger'], alpha=0.6, label='回撤')
        ax.plot(returns_df['date'], drawdown, 
               color=self.colors['danger'], linewidth=1)
        
        ax.set_title('回撤曲线', fontsize=16, fontweight='bold')
        ax.set_xlabel('日期', fontsize=12)
        ax.set_ylabel('回撤 (%)', fontsize=12)
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 格式化x轴
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        ax.xaxis.set_major_locator(mdates.MonthLocator(interval=1))
        plt.xticks(rotation=45)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
    
    def plot_decision_distribution(self, returns_df: pd.DataFrame, 
                                 save_path: Optional[str] = None) -> None:
        """
        绘制决策分布图
        
        Args:
            returns_df: 收益DataFrame
            save_path: 保存路径
        """
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        if returns_df.empty:
            for ax in [ax1, ax2]:
                ax.text(0.5, 0.5, '暂无数据', ha='center', va='center', transform=ax.transAxes)
            plt.suptitle('决策分布分析')
            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.show()
            return
        
        # 决策数量分布
        decision_counts = returns_df['decision'].value_counts()
        colors = [self.colors['primary'], self.colors['secondary'], self.colors['success']][:len(decision_counts)]
        
        ax1.pie(decision_counts.values, labels=decision_counts.index, autopct='%1.1f%%',
               colors=colors, startangle=90)
        ax1.set_title('决策数量分布', fontsize=14, fontweight='bold')
        
        # 决策收益分布
        decision_returns = returns_df.groupby('decision')['return_pct'].mean() * 100
        bars = ax2.bar(decision_returns.index, decision_returns.values, 
                      color=[self.colors['success'] if x > 0 else self.colors['danger'] 
                            for x in decision_returns.values])
        
        ax2.set_title('平均收益率按决策类型', fontsize=14, fontweight='bold')
        ax2.set_xlabel('决策类型', fontsize=12)
        ax2.set_ylabel('平均收益率 (%)', fontsize=12)
        ax2.grid(True, alpha=0.3)
        
        # 添加数值标签
        for bar in bars:
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height,
                    f'{height:.2f}%', ha='center', va='bottom')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
    
    def plot_symbol_performance(self, symbol_performance: Dict[str, Dict], 
                              save_path: Optional[str] = None) -> None:
        """
        绘制按股票的性能对比
        
        Args:
            symbol_performance: 按股票的性能数据
            save_path: 保存路径
        """
        if not symbol_performance:
            fig, ax = plt.subplots(figsize=(10, 6))
            ax.text(0.5, 0.5, '暂无数据', ha='center', va='center', transform=ax.transAxes)
            plt.title('股票性能对比')
            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.show()
            return
        
        # 准备数据
        symbols = list(symbol_performance.keys())
        metrics = ['cumulative_return', 'sharpe_ratio', 'max_drawdown', 'win_rate']
        metric_names = ['累计收益率', '夏普比率', '最大回撤', '胜率']
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        axes = axes.flatten()
        
        for i, (metric, name) in enumerate(zip(metrics, metric_names)):
            values = [symbol_performance[symbol].get(metric, 0) for symbol in symbols]
            
            if metric in ['cumulative_return', 'max_drawdown', 'win_rate']:
                values = [v * 100 for v in values]  # 转换为百分比
            
            bars = axes[i].bar(symbols, values, 
                             color=[self.colors['success'] if v > 0 else self.colors['danger'] 
                                   for v in values])
            
            axes[i].set_title(name, fontsize=14, fontweight='bold')
            axes[i].set_ylabel(f'{name} (%)' if metric != 'sharpe_ratio' else name)
            axes[i].grid(True, alpha=0.3)
            
            # 添加数值标签
            for bar in bars:
                height = bar.get_height()
                axes[i].text(bar.get_x() + bar.get_width()/2., height,
                           f'{height:.2f}', ha='center', va='bottom')
            
            # 旋转x轴标签
            axes[i].tick_params(axis='x', rotation=45)
        
        plt.suptitle('股票性能对比', fontsize=16, fontweight='bold')
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
    
    def plot_monthly_returns(self, returns_df: pd.DataFrame, 
                           save_path: Optional[str] = None) -> None:
        """
        绘制月度收益热力图
        
        Args:
            returns_df: 收益DataFrame
            save_path: 保存路径
        """
        fig, ax = plt.subplots(figsize=(12, 8))
        
        if returns_df.empty:
            ax.text(0.5, 0.5, '暂无数据', ha='center', va='center', transform=ax.transAxes)
            plt.title('月度收益热力图')
            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.show()
            return
        
        # 准备数据
        returns_df = returns_df.copy()
        returns_df['date'] = pd.to_datetime(returns_df['date'])
        returns_df['year'] = returns_df['date'].dt.year
        returns_df['month'] = returns_df['date'].dt.month
        
        # 计算月度收益
        monthly_returns = returns_df.groupby(['year', 'month'])['return_pct'].sum().reset_index()
        monthly_returns['return_pct'] *= 100  # 转换为百分比
        
        # 创建透视表
        pivot_table = monthly_returns.pivot(index='year', columns='month', values='return_pct')
        
        # 绘制热力图
        sns.heatmap(pivot_table, annot=True, fmt='.2f', cmap='RdYlGn', center=0,
                   cbar_kws={'label': '月度收益率 (%)'}, ax=ax)
        
        ax.set_title('月度收益热力图', fontsize=16, fontweight='bold')
        ax.set_xlabel('月份', fontsize=12)
        ax.set_ylabel('年份', fontsize=12)
        
        # 设置月份标签
        month_labels = ['1月', '2月', '3月', '4月', '5月', '6月',
                       '7月', '8月', '9月', '10月', '11月', '12月']
        ax.set_xticklabels(month_labels)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
    
    def generate_comprehensive_report(self, returns_df: pd.DataFrame,
                                    performance_report: Dict[str, Any],
                                    benchmark_data: Optional[pd.DataFrame] = None,
                                    output_dir: Optional[str] = None) -> None:
        """
        生成综合可视化报告
        
        Args:
            returns_df: 收益DataFrame
            performance_report: 性能报告
            benchmark_data: 基准数据
            output_dir: 输出目录
        """
        if output_dir:
            self.output_dir = Path(output_dir)
            self.output_dir.mkdir(parents=True, exist_ok=True)
        
        print("📊 生成可视化报告...")
        
        # 1. 累计收益曲线
        self.plot_cumulative_returns(
            returns_df, benchmark_data,
            save_path=self.output_dir / "cumulative_returns.png"
        )
        
        # 2. 回撤曲线
        self.plot_drawdown(
            returns_df,
            save_path=self.output_dir / "drawdown.png"
        )
        
        # 3. 决策分布
        self.plot_decision_distribution(
            returns_df,
            save_path=self.output_dir / "decision_distribution.png"
        )
        
        # 4. 股票性能对比
        symbol_performance = performance_report.get('symbol_performance', {})
        if symbol_performance:
            self.plot_symbol_performance(
                symbol_performance,
                save_path=self.output_dir / "symbol_performance.png"
            )
        
        # 5. 月度收益热力图
        self.plot_monthly_returns(
            returns_df,
            save_path=self.output_dir / "monthly_returns.png"
        )
        
        print(f"✅ 可视化报告已生成: {self.output_dir}")
    
    def save_performance_table(self, performance_report: Dict[str, Any],
                             save_path: str) -> None:
        """
        保存性能指标表格
        
        Args:
            performance_report: 性能报告
            save_path: 保存路径
        """
        portfolio = performance_report.get('portfolio_performance', {})
        
        # 创建性能指标表格
        metrics_data = {
            '指标': ['总交易次数', '胜率', '累计收益率', '年化波动率', '夏普比率', '最大回撤'],
            '数值': [
                portfolio.get('total_trades', 0),
                f"{portfolio.get('win_rate', 0):.2%}",
                f"{portfolio.get('cumulative_return', 0):.2%}",
                f"{portfolio.get('volatility', 0):.2%}",
                f"{portfolio.get('sharpe_ratio', 0):.3f}",
                f"{portfolio.get('max_drawdown', 0):.2%}"
            ]
        }
        
        df = pd.DataFrame(metrics_data)
        df.to_csv(save_path, index=False, encoding='utf-8')
        print(f"✅ 性能指标表格已保存: {save_path}")
