#!/usr/bin/env python3
"""
在线数据获取脚本
用于调用在线模式工具获取四个分析师代理所需的数据并保存到本地

使用方法:
python scripts/fetch_online_data.py --symbols AAPL,MSFT --start-date 2024-01-01 --end-date 2024-01-31
"""

import os
import sys
import json
import argparse
import pandas as pd
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Any
import time

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 导入TradingAgents模块
from tradingagents.dataflows import interface
from tradingagents.default_config import DEFAULT_CONFIG
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()


class OnlineDataFetcher:
    """在线数据获取器"""

    def __init__(self, data_dir: str = None):
        """
        初始化数据获取器

        Args:
            data_dir: 数据保存目录，默认使用配置中的data_dir
        """
        self.data_dir = Path(data_dir) if data_dir else Path(DEFAULT_CONFIG["data_dir"])
        self.cache_dir = Path(DEFAULT_CONFIG["data_cache_dir"])

        # 确保目录存在
        self.data_dir.mkdir(parents=True, exist_ok=True)
        self.cache_dir.mkdir(parents=True, exist_ok=True)

        # 默认配置
        self.delay = 1.0  # API调用间隔
        self.max_retries = 3  # 最大重试次数
        self.retry_delay = 2.0  # 重试间隔
        self.verbose = False

        # 检查API密钥
        self._check_api_keys()

        print(f"📁 数据目录: {self.data_dir}")
        print(f"📁 缓存目录: {self.cache_dir}")
    
    def _check_api_keys(self):
        """检查必需的API密钥"""
        required_keys = {
            'DEEPSEEK_API_KEY': '用于LLM分析',
            'FINNHUB_API_KEY': '用于金融数据'
        }

        missing_keys = []
        for key, desc in required_keys.items():
            if not os.getenv(key):
                missing_keys.append(f"{key} ({desc})")

        if missing_keys:
            print("⚠️  缺少以下API密钥:")
            for key in missing_keys:
                print(f"   - {key}")
            print("\n请在.env文件中配置这些密钥")
        else:
            print("✅ API密钥检查通过")

    def _retry_api_call(self, func, *args, **kwargs):
        """
        带重试机制的API调用

        Args:
            func: 要调用的函数
            *args: 函数参数
            **kwargs: 函数关键字参数

        Returns:
            函数执行结果
        """
        last_error = None

        for attempt in range(self.max_retries):
            try:
                if attempt > 0:
                    wait_time = self.retry_delay * (2 ** (attempt - 1))  # 指数退避
                    print(f"   ⏳ 第{attempt + 1}次尝试，等待 {wait_time:.1f} 秒...")
                    time.sleep(wait_time)

                result = func(*args, **kwargs)
                return result

            except Exception as e:
                last_error = e
                error_msg = str(e).lower()

                if "rate limit" in error_msg or "too many requests" in error_msg:
                    if attempt < self.max_retries - 1:
                        wait_time = 60 * (attempt + 1)  # 速率限制时等待更长时间
                        print(f"   ⚠️  API速率限制，等待 {wait_time} 秒后重试...")
                        time.sleep(wait_time)
                        continue
                elif "timeout" in error_msg or "connection" in error_msg:
                    if attempt < self.max_retries - 1:
                        print(f"   ⚠️  网络问题，{self.retry_delay} 秒后重试...")
                        time.sleep(self.retry_delay)
                        continue

                if attempt == self.max_retries - 1:
                    print(f"   ❌ 达到最大重试次数，最后错误: {e}")
                    break

        raise last_error
    
    def fetch_market_data(self, symbols: List[str], start_date: str, end_date: str) -> Dict[str, Any]:
        """
        获取市场分析师数据

        Args:
            symbols: 股票代码列表
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)

        Returns:
            获取的数据字典
        """
        print("\n🏢 获取市场分析师数据...")
        market_data = {}

        for i, symbol in enumerate(symbols):
            print(f"📊 处理股票: {symbol} ({i+1}/{len(symbols)})")

            try:
                # 1. 获取Yahoo Finance价格数据
                print(f"   获取价格数据...")
                price_data = self._retry_api_call(
                    interface.get_YFin_data_online,
                    symbol, start_date, end_date
                )

                # 保存价格数据
                price_file = self.data_dir / "market_data" / "price_data" / f"{symbol}-YFin-data-{start_date}-{end_date}.csv"
                price_file.parent.mkdir(parents=True, exist_ok=True)

                if isinstance(price_data, pd.DataFrame):
                    price_data.to_csv(price_file, index=False)
                    print(f"   ✅ 价格数据已保存: {price_file}")
                    data_points = len(price_data)
                    print(f"      📈 数据点数: {data_points}")
                else:
                    # 如果返回的是字符串，保存为文本文件
                    with open(price_file.with_suffix('.txt'), 'w', encoding='utf-8') as f:
                        f.write(str(price_data))
                    print(f"   ✅ 价格数据已保存: {price_file.with_suffix('.txt')}")

                # 添加延迟
                time.sleep(self.delay)

                # 2. 获取技术指标数据 (只有在价格数据成功时才获取)
                if isinstance(price_data, pd.DataFrame) and not price_data.empty:
                    print(f"   获取技术指标...")
                    indicators = ['rsi_14', 'macd', 'boll', 'ma_20', 'ema_12']
                    indicator_data = {}

                    for indicator in indicators:
                        try:
                            indicator_result = self._retry_api_call(
                                interface.get_stock_stats_indicators_window,
                                symbol, indicator, end_date, 30, True  # online=True
                            )
                            indicator_data[indicator] = indicator_result
                            print(f"     ✅ {indicator}")
                            time.sleep(self.delay * 0.5)  # 技术指标间隔稍短
                        except Exception as e:
                            print(f"     ❌ {indicator}: {e}")
                            indicator_data[indicator] = f"Error: {e}"

                    # 保存技术指标数据
                    indicator_file = self.data_dir / "market_data" / "indicators" / f"{symbol}-indicators-{end_date}.json"
                    indicator_file.parent.mkdir(parents=True, exist_ok=True)

                    with open(indicator_file, 'w', encoding='utf-8') as f:
                        json.dump(indicator_data, f, ensure_ascii=False, indent=2)
                    print(f"   ✅ 技术指标已保存: {indicator_file}")

                    market_data[symbol] = {
                        'price_data': str(price_file),
                        'indicators': indicator_data,
                        'data_points': data_points if isinstance(price_data, pd.DataFrame) else 0
                    }
                else:
                    print(f"   ⚠️  价格数据无效，跳过技术指标获取")
                    market_data[symbol] = {
                        'price_data': str(price_file),
                        'error': 'Invalid price data'
                    }

            except Exception as e:
                print(f"   ❌ 获取 {symbol} 数据失败: {e}")
                market_data[symbol] = {'error': str(e)}

            # 股票间延迟
            if i < len(symbols) - 1:
                print(f"   ⏳ 等待 {self.delay} 秒后处理下一个股票...")
                time.sleep(self.delay)

        return market_data
    
    def fetch_news_data(self, symbols: List[str], target_date: str) -> Dict[str, Any]:
        """
        获取新闻分析师数据

        Args:
            symbols: 股票代码列表
            target_date: 目标日期 (YYYY-MM-DD)

        Returns:
            获取的数据字典
        """
        print("\n📰 获取新闻分析师数据...")
        news_data = {}

        try:
            # 1. 获取全球新闻
            print("   获取全球新闻...")
            global_news = self._retry_api_call(
                interface.get_global_news_openai,
                target_date
            )

            # 检查返回的内容质量
            if self._is_valid_news_content(global_news):
                # 保存全球新闻
                global_news_file = self.data_dir / "news_data" / "global" / f"global-news-{target_date}.txt"
                global_news_file.parent.mkdir(parents=True, exist_ok=True)

                with open(global_news_file, 'w', encoding='utf-8') as f:
                    f.write(global_news)
                print(f"   ✅ 全球新闻已保存: {global_news_file}")
                print(f"      📄 内容长度: {len(global_news)} 字符")

                news_data['global_news'] = {
                    'file': str(global_news_file),
                    'content': global_news[:500] + "..." if len(global_news) > 500 else global_news,
                    'content_length': len(global_news)
                }
            else:
                print(f"   ⚠️  全球新闻内容质量不佳，可能是指导性文本而非实际新闻")
                news_data['global_news'] = {
                    'error': 'Low quality content - appears to be instructional text',
                    'raw_content': global_news[:200] + "..."
                }

            time.sleep(self.delay)

            # 2. 获取Google新闻
            for i, symbol in enumerate(symbols):
                print(f"   获取 {symbol} Google新闻... ({i+1}/{len(symbols)})")
                try:
                    google_news = self._retry_api_call(
                        interface.get_google_news,
                        symbol, target_date, 7
                    )

                    # 保存Google新闻
                    google_news_file = self.data_dir / "news_data" / "google" / f"{symbol}-google-news-{target_date}.txt"
                    google_news_file.parent.mkdir(parents=True, exist_ok=True)

                    with open(google_news_file, 'w', encoding='utf-8') as f:
                        f.write(str(google_news))

                    if google_news and str(google_news).strip():
                        print(f"     ✅ Google新闻已保存: {google_news_file}")
                        print(f"        📄 内容长度: {len(str(google_news))} 字符")

                        news_data[f'{symbol}_google_news'] = {
                            'file': str(google_news_file),
                            'content': str(google_news)[:300] + "..." if len(str(google_news)) > 300 else str(google_news),
                            'content_length': len(str(google_news))
                        }
                    else:
                        print(f"     ⚠️  Google新闻内容为空")
                        news_data[f'{symbol}_google_news'] = {
                            'file': str(google_news_file),
                            'error': 'Empty content',
                            'content_length': 0
                        }

                    time.sleep(self.delay)

                except Exception as e:
                    print(f"     ❌ 获取 {symbol} Google新闻失败: {e}")
                    news_data[f'{symbol}_google_news'] = {'error': str(e)}

        except Exception as e:
            print(f"   ❌ 获取新闻数据失败: {e}")
            news_data['error'] = str(e)

        return news_data

    def _is_valid_news_content(self, content: str) -> bool:
        """
        检查新闻内容是否有效（不是指导性文本）

        Args:
            content: 新闻内容

        Returns:
            是否为有效新闻内容
        """
        if not content or len(content.strip()) < 50:
            return False

        # 检查是否包含指导性关键词
        instructional_keywords = [
            "search for", "you can use", "here are some", "to find",
            "check these", "look for", "try these", "visit these",
            "use the following", "here's how", "follow these steps"
        ]

        content_lower = content.lower()
        for keyword in instructional_keywords:
            if keyword in content_lower:
                return False

        return True
    
    def fetch_social_media_data(self, symbols: List[str], target_date: str) -> Dict[str, Any]:
        """
        获取社交媒体分析师数据

        Args:
            symbols: 股票代码列表
            target_date: 目标日期 (YYYY-MM-DD)

        Returns:
            获取的数据字典
        """
        print("\n📱 获取社交媒体分析师数据...")
        social_data = {}

        for i, symbol in enumerate(symbols):
            print(f"   获取 {symbol} 社交媒体数据... ({i+1}/{len(symbols)})")
            try:
                # 获取股票相关社交媒体数据
                social_news = self._retry_api_call(
                    interface.get_stock_news_openai,
                    symbol, target_date
                )

                # 保存社交媒体数据
                social_file = self.data_dir / "social_data" / f"{symbol}-social-{target_date}.txt"
                social_file.parent.mkdir(parents=True, exist_ok=True)

                with open(social_file, 'w', encoding='utf-8') as f:
                    f.write(social_news)

                # 检查内容质量
                if self._is_valid_social_content(social_news):
                    print(f"     ✅ 社交媒体数据已保存: {social_file}")
                    print(f"        📄 内容长度: {len(social_news)} 字符")

                    social_data[symbol] = {
                        'file': str(social_file),
                        'content': social_news[:300] + "..." if len(social_news) > 300 else social_news,
                        'content_length': len(social_news)
                    }
                else:
                    print(f"     ⚠️  社交媒体数据质量不佳，可能是指导性文本")
                    social_data[symbol] = {
                        'file': str(social_file),
                        'error': 'Low quality content - appears to be instructional text',
                        'raw_content': social_news[:200] + "..."
                    }

                time.sleep(self.delay)

            except Exception as e:
                print(f"     ❌ 获取 {symbol} 社交媒体数据失败: {e}")
                social_data[symbol] = {'error': str(e)}

        return social_data

    def _is_valid_social_content(self, content: str) -> bool:
        """
        检查社交媒体内容是否有效

        Args:
            content: 社交媒体内容

        Returns:
            是否为有效内容
        """
        if not content or len(content.strip()) < 50:
            return False

        # 检查是否包含指导性关键词
        instructional_keywords = [
            "here's how you can search", "use the advanced search",
            "try these", "follow these steps", "use the search bar",
            "search for", "filter by", "example url"
        ]

        content_lower = content.lower()
        for keyword in instructional_keywords:
            if keyword in content_lower:
                return False

        return True
    
    def fetch_fundamentals_data(self, symbols: List[str], target_date: str) -> Dict[str, Any]:
        """
        获取基本面分析师数据

        Args:
            symbols: 股票代码列表
            target_date: 目标日期 (YYYY-MM-DD)

        Returns:
            获取的数据字典
        """
        print("\n📈 获取基本面分析师数据...")
        fundamentals_data = {}

        for i, symbol in enumerate(symbols):
            print(f"   获取 {symbol} 基本面数据... ({i+1}/{len(symbols)})")
            try:
                # 获取基本面数据
                fundamentals = self._retry_api_call(
                    interface.get_fundamentals_openai,
                    symbol, target_date
                )

                # 保存基本面数据
                fundamentals_file = self.data_dir / "fundamentals_data" / f"{symbol}-fundamentals-{target_date}.txt"
                fundamentals_file.parent.mkdir(parents=True, exist_ok=True)

                with open(fundamentals_file, 'w', encoding='utf-8') as f:
                    f.write(fundamentals)

                # 检查内容质量
                if self._is_valid_fundamentals_content(fundamentals):
                    print(f"     ✅ 基本面数据已保存: {fundamentals_file}")
                    print(f"        📄 内容长度: {len(fundamentals)} 字符")

                    fundamentals_data[symbol] = {
                        'file': str(fundamentals_file),
                        'content': fundamentals[:400] + "..." if len(fundamentals) > 400 else fundamentals,
                        'content_length': len(fundamentals)
                    }
                else:
                    print(f"     ⚠️  基本面数据质量不佳，可能缺少实际财务数据")
                    fundamentals_data[symbol] = {
                        'file': str(fundamentals_file),
                        'error': 'Low quality content - may lack actual financial data',
                        'raw_content': fundamentals[:200] + "..."
                    }

                time.sleep(self.delay)

            except Exception as e:
                print(f"     ❌ 获取 {symbol} 基本面数据失败: {e}")
                fundamentals_data[symbol] = {'error': str(e)}

        return fundamentals_data

    def _is_valid_fundamentals_content(self, content: str) -> bool:
        """
        检查基本面内容是否有效

        Args:
            content: 基本面内容

        Returns:
            是否为有效内容
        """
        if not content or len(content.strip()) < 100:
            return False

        # 检查是否包含实际的财务数据指标
        financial_indicators = [
            "p/e", "pe ratio", "price-to-earnings", "p/s", "ps ratio",
            "revenue", "net income", "cash flow", "dividend", "earnings",
            "market cap", "debt", "assets", "equity", "$", "billion", "million"
        ]

        content_lower = content.lower()
        indicator_count = sum(1 for indicator in financial_indicators if indicator in content_lower)

        # 如果包含多个财务指标，认为是有效内容
        return indicator_count >= 3
    
    def fetch_all_data(self, symbols: List[str], start_date: str, end_date: str) -> Dict[str, Any]:
        """
        获取所有分析师数据
        
        Args:
            symbols: 股票代码列表
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            
        Returns:
            所有获取的数据
        """
        print(f"🚀 开始获取数据...")
        print(f"   股票代码: {', '.join(symbols)}")
        print(f"   时间范围: {start_date} ~ {end_date}")
        
        all_data = {
            'metadata': {
                'symbols': symbols,
                'start_date': start_date,
                'end_date': end_date,
                'fetch_time': datetime.now().isoformat()
            }
        }
        
        # 获取各类数据
        all_data['market_data'] = self.fetch_market_data(symbols, start_date, end_date)
        all_data['news_data'] = self.fetch_news_data(symbols, end_date)
        all_data['social_data'] = self.fetch_social_media_data(symbols, end_date)
        all_data['fundamentals_data'] = self.fetch_fundamentals_data(symbols, end_date)
        
        # 保存汇总信息
        summary_file = self.data_dir / f"data_summary_{end_date}.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(all_data, f, ensure_ascii=False, indent=2)
        
        print(f"\n✅ 数据获取完成!")
        print(f"📄 汇总文件: {summary_file}")
        
        return all_data


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='获取TradingAgents在线数据',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 获取单个股票数据
  python scripts/fetch_online_data.py --symbols AAPL --start-date 2024-01-01 --end-date 2024-01-31

  # 获取多个股票数据
  python scripts/fetch_online_data.py --symbols AAPL,MSFT,GOOGL --start-date 2024-01-01 --end-date 2024-01-31

  # 指定自定义数据目录
  python scripts/fetch_online_data.py --symbols AAPL --start-date 2024-01-01 --end-date 2024-01-31 --data-dir ./my_data

  # 只获取特定类型的数据
  python scripts/fetch_online_data.py --symbols AAPL --start-date 2024-01-01 --end-date 2024-01-31 --analysts market,news

注意事项:
  - 需要配置 DEEPSEEK_API_KEY 和 FINNHUB_API_KEY 环境变量
  - 建议使用较短的时间范围以节省API调用成本
  - 数据将保存在 data/ 目录下的相应子目录中
        """
    )

    parser.add_argument('--symbols', required=True,
                       help='股票代码，用逗号分隔，如: AAPL,MSFT,GOOGL')
    parser.add_argument('--start-date', required=True,
                       help='开始日期 (YYYY-MM-DD)')
    parser.add_argument('--end-date', required=True,
                       help='结束日期 (YYYY-MM-DD)')
    parser.add_argument('--data-dir',
                       help='数据保存目录，默认使用配置中的目录')
    parser.add_argument('--analysts',
                       help='指定要获取的分析师数据，用逗号分隔: market,news,social,fundamentals (默认获取全部)')
    parser.add_argument('--delay', type=float, default=1.0,
                       help='API调用间隔时间(秒)，默认1秒')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='显示详细输出')

    args = parser.parse_args()

    # 解析股票代码
    symbols = [s.strip().upper() for s in args.symbols.split(',')]

    # 验证日期格式
    try:
        start_dt = datetime.strptime(args.start_date, '%Y-%m-%d')
        end_dt = datetime.strptime(args.end_date, '%Y-%m-%d')

        if start_dt > end_dt:
            print("❌ 开始日期不能晚于结束日期")
            return

        if end_dt > datetime.now():
            print("⚠️  结束日期晚于当前日期，某些数据可能无法获取")

    except ValueError:
        print("❌ 日期格式错误，请使用 YYYY-MM-DD 格式")
        return

    # 解析分析师类型
    if args.analysts:
        selected_analysts = [a.strip().lower() for a in args.analysts.split(',')]
        valid_analysts = ['market', 'news', 'social', 'fundamentals']
        invalid_analysts = [a for a in selected_analysts if a not in valid_analysts]
        if invalid_analysts:
            print(f"❌ 无效的分析师类型: {', '.join(invalid_analysts)}")
            print(f"   有效类型: {', '.join(valid_analysts)}")
            return
    else:
        selected_analysts = ['market', 'news', 'social', 'fundamentals']

    print("🔧 配置信息:")
    print(f"   股票代码: {', '.join(symbols)}")
    print(f"   时间范围: {args.start_date} ~ {args.end_date}")
    print(f"   分析师类型: {', '.join(selected_analysts)}")
    print(f"   API调用间隔: {args.delay}秒")

    try:
        # 创建数据获取器
        fetcher = OnlineDataFetcher(args.data_dir)
        fetcher.delay = args.delay
        fetcher.verbose = args.verbose

        # 执行数据获取
        all_data = {}
        all_data['metadata'] = {
            'symbols': symbols,
            'start_date': args.start_date,
            'end_date': args.end_date,
            'selected_analysts': selected_analysts,
            'fetch_time': datetime.now().isoformat()
        }

        # 根据选择获取数据
        if 'market' in selected_analysts:
            all_data['market_data'] = fetcher.fetch_market_data(symbols, args.start_date, args.end_date)

        if 'news' in selected_analysts:
            all_data['news_data'] = fetcher.fetch_news_data(symbols, args.end_date)

        if 'social' in selected_analysts:
            all_data['social_data'] = fetcher.fetch_social_media_data(symbols, args.end_date)

        if 'fundamentals' in selected_analysts:
            all_data['fundamentals_data'] = fetcher.fetch_fundamentals_data(symbols, args.end_date)

        # 保存汇总信息
        summary_file = fetcher.data_dir / f"data_summary_{args.end_date}.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(all_data, f, ensure_ascii=False, indent=2)

        print(f"\n🎉 数据获取完成!")
        print(f"📄 汇总文件: {summary_file}")
        print(f"📁 数据目录: {fetcher.data_dir}")

        # 显示统计信息
        total_files = sum(len(list(Path(fetcher.data_dir).rglob('*'))) for _ in [None])
        print(f"📊 共生成 {total_files} 个文件")

    except KeyboardInterrupt:
        print("\n⏹️  用户中断操作")
    except Exception as e:
        print(f"\n❌ 执行失败: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()


if __name__ == '__main__':
    main()
