#!/usr/bin/env python3
"""
Setup script for TradingAgents with DeepSeek integration.
This script helps users configure their environment for DeepSeek API usage.
"""

import os
import sys
import subprocess
from pathlib import Path


def print_banner():
    """Print setup banner."""
    print("=" * 60)
    print("🚀 TradingAgents DeepSeek Setup")
    print("=" * 60)
    print()


def check_python_version():
    """Check if Python version is compatible."""
    print("🐍 Checking Python version...")
    
    if sys.version_info < (3, 10):
        print("❌ Python 3.10 or higher is required")
        print(f"   Current version: {sys.version}")
        return False
    
    print(f"✅ Python {sys.version.split()[0]} is compatible")
    return True


def install_dependencies():
    """Install required dependencies."""
    print("\n📦 Installing dependencies...")
    
    try:
        # Install requirements
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False


def setup_env_file():
    """Set up .env file from template."""
    print("\n⚙️ Setting up environment file...")
    
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if env_file.exists():
        print("⚠️ .env file already exists")
        response = input("Do you want to overwrite it? (y/N): ").lower()
        if response != 'y':
            print("📝 Keeping existing .env file")
            return True
    
    if env_example.exists():
        # Copy example to .env
        with open(env_example, 'r') as src, open(env_file, 'w') as dst:
            dst.write(src.read())
        print("✅ Created .env file from template")
    else:
        # Create basic .env file
        env_content = """# TradingAgents Environment Configuration

# DeepSeek API (Primary LLM Provider - Recommended)
# Get your API key from: https://platform.deepseek.com/api_keys
DEEPSEEK_API_KEY=your_deepseek_api_key_here

# FinnHub API (Required for financial data)
# Get your free API key from: https://finnhub.io/register
FINNHUB_API_KEY=your_finnhub_api_key_here

# OpenAI API (Optional - Fallback LLM Provider)
# OPENAI_API_KEY=your_openai_api_key_here
"""
        with open(env_file, 'w') as f:
            f.write(env_content)
        print("✅ Created basic .env file")
    
    return True


def prompt_for_api_keys():
    """Prompt user to enter API keys."""
    print("\n🔑 API Key Configuration")
    print("Please obtain and enter your API keys:")
    print()
    
    # DeepSeek API Key
    print("1. DeepSeek API Key:")
    print("   - Visit: https://platform.deepseek.com/api_keys")
    print("   - Sign up and create an API key")
    deepseek_key = input("   Enter your DeepSeek API key (or press Enter to skip): ").strip()
    
    # FinnHub API Key
    print("\n2. FinnHub API Key:")
    print("   - Visit: https://finnhub.io/register")
    print("   - Sign up for free and get your API key")
    finnhub_key = input("   Enter your FinnHub API key (or press Enter to skip): ").strip()
    
    # Update .env file
    if deepseek_key or finnhub_key:
        env_file = Path(".env")
        if env_file.exists():
            with open(env_file, 'r') as f:
                content = f.read()
            
            if deepseek_key:
                content = content.replace("DEEPSEEK_API_KEY=your_deepseek_api_key_here", 
                                        f"DEEPSEEK_API_KEY={deepseek_key}")
            
            if finnhub_key:
                content = content.replace("FINNHUB_API_KEY=your_finnhub_api_key_here", 
                                        f"FINNHUB_API_KEY={finnhub_key}")
            
            with open(env_file, 'w') as f:
                f.write(content)
            
            print("✅ API keys updated in .env file")
    
    return True


def run_test():
    """Run the test script to verify setup."""
    print("\n🧪 Running setup verification test...")
    
    try:
        result = subprocess.run([sys.executable, "test_deepseek.py"], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Setup verification passed!")
            return True
        else:
            print("⚠️ Setup verification had issues:")
            print(result.stdout)
            if result.stderr:
                print("Errors:")
                print(result.stderr)
            return False
    except FileNotFoundError:
        print("⚠️ Test script not found, skipping verification")
        return True


def print_next_steps():
    """Print next steps for the user."""
    print("\n🎉 Setup Complete!")
    print("=" * 60)
    print()
    print("Next steps:")
    print("1. Edit .env file and add your API keys if you haven't already")
    print("2. Test the setup:")
    print("   python test_deepseek.py")
    print()
    print("3. Run TradingAgents:")
    print("   python main.py                    # Basic usage")
    print("   python -m cli.main               # Interactive CLI")
    print()
    print("4. For help and documentation:")
    print("   - Check README.md for detailed instructions")
    print("   - Visit: https://github.com/TauricResearch/TradingAgents")
    print()


def main():
    """Main setup function."""
    print_banner()
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        print("\n❌ Setup failed during dependency installation")
        sys.exit(1)
    
    # Setup .env file
    if not setup_env_file():
        print("\n❌ Setup failed during .env file creation")
        sys.exit(1)
    
    # Prompt for API keys
    prompt_for_api_keys()
    
    # Run verification test
    run_test()
    
    # Print next steps
    print_next_steps()


if __name__ == "__main__":
    main()
