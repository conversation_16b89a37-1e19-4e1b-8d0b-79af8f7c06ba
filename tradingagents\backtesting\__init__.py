"""
TradingAgents 回测框架

这个模块提供了一个全面的回测框架，支持：
- 日期范围选择和验证
- 多模型配置和选择
- 多代理回测场景
- 性能分析和可视化
- 结果导出和报告生成
"""

from .config import BacktestConfig, ModelConfig, AgentConfig
from .engine import BacktestEngine
from .performance import PerformanceAnalyzer
from .visualization import ResultVisualizer
from .experiment import ExperimentManager

__all__ = [
    "BacktestConfig",
    "ModelConfig", 
    "AgentConfig",
    "BacktestEngine",
    "PerformanceAnalyzer",
    "ResultVisualizer",
    "ExperimentManager",
]

__version__ = "1.0.0"
