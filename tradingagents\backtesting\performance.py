"""
性能分析模块

计算和分析回测结果的性能指标，包括收益率、风险指标、基准比较等。
"""

import numpy as np
import pandas as pd
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any
import warnings
warnings.filterwarnings('ignore')


class PerformanceAnalyzer:
    """性能分析器"""
    
    def __init__(self, risk_free_rate: float = 0.02):
        """
        初始化性能分析器
        
        Args:
            risk_free_rate: 无风险利率（年化）
        """
        self.risk_free_rate = risk_free_rate
    
    def calculate_returns(self, trades_df: pd.DataFrame, 
                         price_data: Dict[str, pd.DataFrame]) -> pd.DataFrame:
        """
        计算交易收益
        
        Args:
            trades_df: 交易记录DataFrame
            price_data: 价格数据字典 {symbol: price_df}
            
        Returns:
            包含收益的DataFrame
        """
        results = []
        
        for _, trade in trades_df.iterrows():
            symbol = trade['symbol']
            date = trade['date']
            decision = trade['decision']
            
            if symbol not in price_data:
                continue
            
            price_df = price_data[symbol].copy()
            price_df['Date'] = pd.to_datetime(price_df['Date'])
            
            # 找到交易日期的价格
            trade_date = pd.to_datetime(date)
            trade_price_row = price_df[price_df['Date'] <= trade_date].tail(1)
            
            if trade_price_row.empty:
                continue
            
            trade_price = trade_price_row['Close'].iloc[0]
            
            # 计算持有期收益（假设持有1周）
            future_date = trade_date + pd.Timedelta(days=7)
            future_price_row = price_df[price_df['Date'] <= future_date].tail(1)
            
            if future_price_row.empty:
                continue
            
            future_price = future_price_row['Close'].iloc[0]
            
            # 根据决策计算收益
            if decision.upper() == 'BUY':
                return_pct = (future_price - trade_price) / trade_price
            elif decision.upper() == 'SELL':
                return_pct = (trade_price - future_price) / trade_price
            else:  # HOLD or other
                return_pct = 0.0
            
            results.append({
                'symbol': symbol,
                'date': date,
                'decision': decision,
                'entry_price': trade_price,
                'exit_price': future_price,
                'return_pct': return_pct,
                'return_abs': return_pct * 10000  # 假设每次投资10000
            })
        
        return pd.DataFrame(results)
    
    def calculate_portfolio_performance(self, returns_df: pd.DataFrame) -> Dict[str, float]:
        """
        计算投资组合性能指标
        
        Args:
            returns_df: 收益DataFrame
            
        Returns:
            性能指标字典
        """
        if returns_df.empty:
            return self._empty_performance()
        
        returns = returns_df['return_pct'].values
        
        # 基本统计
        total_trades = len(returns)
        winning_trades = len(returns[returns > 0])
        losing_trades = len(returns[returns < 0])
        
        # 收益指标
        total_return = np.sum(returns)
        avg_return = np.mean(returns)
        cumulative_return = np.prod(1 + returns) - 1
        
        # 风险指标
        volatility = np.std(returns) * np.sqrt(252)  # 年化波动率
        downside_returns = returns[returns < 0]
        downside_volatility = np.std(downside_returns) * np.sqrt(252) if len(downside_returns) > 0 else 0
        
        # 夏普比率
        excess_return = avg_return - self.risk_free_rate / 252
        sharpe_ratio = excess_return / (volatility / np.sqrt(252)) if volatility > 0 else 0
        
        # 索提诺比率
        sortino_ratio = excess_return / (downside_volatility / np.sqrt(252)) if downside_volatility > 0 else 0
        
        # 最大回撤
        cumulative_returns = np.cumprod(1 + returns)
        running_max = np.maximum.accumulate(cumulative_returns)
        drawdown = (cumulative_returns - running_max) / running_max
        max_drawdown = np.min(drawdown)
        
        # 胜率
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        # 盈亏比
        avg_win = np.mean(returns[returns > 0]) if winning_trades > 0 else 0
        avg_loss = np.mean(returns[returns < 0]) if losing_trades > 0 else 0
        profit_loss_ratio = abs(avg_win / avg_loss) if avg_loss != 0 else 0
        
        # 卡尔马比率
        calmar_ratio = cumulative_return / abs(max_drawdown) if max_drawdown != 0 else 0
        
        return {
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'win_rate': win_rate,
            'total_return': total_return,
            'cumulative_return': cumulative_return,
            'avg_return': avg_return,
            'volatility': volatility,
            'downside_volatility': downside_volatility,
            'sharpe_ratio': sharpe_ratio,
            'sortino_ratio': sortino_ratio,
            'max_drawdown': max_drawdown,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'profit_loss_ratio': profit_loss_ratio,
            'calmar_ratio': calmar_ratio
        }
    
    def calculate_symbol_performance(self, returns_df: pd.DataFrame) -> Dict[str, Dict[str, float]]:
        """
        按股票计算性能指标
        
        Args:
            returns_df: 收益DataFrame
            
        Returns:
            按股票分组的性能指标
        """
        symbol_performance = {}
        
        for symbol in returns_df['symbol'].unique():
            symbol_returns = returns_df[returns_df['symbol'] == symbol]
            symbol_performance[symbol] = self.calculate_portfolio_performance(symbol_returns)
        
        return symbol_performance
    
    def calculate_benchmark_comparison(self, returns_df: pd.DataFrame, 
                                     benchmark_data: pd.DataFrame) -> Dict[str, float]:
        """
        与基准比较
        
        Args:
            returns_df: 策略收益DataFrame
            benchmark_data: 基准数据DataFrame
            
        Returns:
            比较指标
        """
        if returns_df.empty or benchmark_data.empty:
            return {}
        
        # 计算基准收益
        benchmark_data = benchmark_data.copy()
        benchmark_data['Date'] = pd.to_datetime(benchmark_data['Date'])
        benchmark_data = benchmark_data.sort_values('Date')
        benchmark_data['benchmark_return'] = benchmark_data['Close'].pct_change()
        
        # 对齐日期
        returns_df = returns_df.copy()
        returns_df['Date'] = pd.to_datetime(returns_df['date'])
        
        merged = pd.merge(returns_df, benchmark_data[['Date', 'benchmark_return']], 
                         on='Date', how='left')
        
        strategy_returns = merged['return_pct'].values
        benchmark_returns = merged['benchmark_return'].fillna(0).values
        
        # 计算超额收益
        excess_returns = strategy_returns - benchmark_returns
        
        # 信息比率
        tracking_error = np.std(excess_returns) * np.sqrt(252)
        information_ratio = np.mean(excess_returns) / tracking_error if tracking_error > 0 else 0
        
        # Beta
        covariance = np.cov(strategy_returns, benchmark_returns)[0, 1]
        benchmark_variance = np.var(benchmark_returns)
        beta = covariance / benchmark_variance if benchmark_variance > 0 else 0
        
        # Alpha
        strategy_avg_return = np.mean(strategy_returns)
        benchmark_avg_return = np.mean(benchmark_returns)
        alpha = strategy_avg_return - (self.risk_free_rate / 252 + beta * (benchmark_avg_return - self.risk_free_rate / 252))
        
        return {
            'alpha': alpha * 252,  # 年化
            'beta': beta,
            'information_ratio': information_ratio,
            'tracking_error': tracking_error,
            'correlation': np.corrcoef(strategy_returns, benchmark_returns)[0, 1]
        }
    
    def generate_performance_report(self, returns_df: pd.DataFrame, 
                                  benchmark_data: Optional[pd.DataFrame] = None) -> Dict[str, Any]:
        """
        生成完整的性能报告
        
        Args:
            returns_df: 收益DataFrame
            benchmark_data: 基准数据（可选）
            
        Returns:
            完整的性能报告
        """
        report = {
            'generated_at': datetime.now().isoformat(),
            'analysis_period': {
                'start_date': returns_df['date'].min() if not returns_df.empty else None,
                'end_date': returns_df['date'].max() if not returns_df.empty else None,
                'total_days': len(returns_df['date'].unique()) if not returns_df.empty else 0
            }
        }
        
        # 整体性能
        report['portfolio_performance'] = self.calculate_portfolio_performance(returns_df)
        
        # 按股票性能
        report['symbol_performance'] = self.calculate_symbol_performance(returns_df)
        
        # 基准比较
        if benchmark_data is not None:
            report['benchmark_comparison'] = self.calculate_benchmark_comparison(returns_df, benchmark_data)
        
        # 决策分析
        if not returns_df.empty:
            decision_analysis = {}
            for decision in returns_df['decision'].unique():
                decision_returns = returns_df[returns_df['decision'] == decision]
                decision_analysis[decision] = {
                    'count': len(decision_returns),
                    'avg_return': decision_returns['return_pct'].mean(),
                    'total_return': decision_returns['return_pct'].sum(),
                    'win_rate': len(decision_returns[decision_returns['return_pct'] > 0]) / len(decision_returns)
                }
            report['decision_analysis'] = decision_analysis
        
        return report
    
    def _empty_performance(self) -> Dict[str, float]:
        """返回空的性能指标"""
        return {
            'total_trades': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'win_rate': 0,
            'total_return': 0,
            'cumulative_return': 0,
            'avg_return': 0,
            'volatility': 0,
            'downside_volatility': 0,
            'sharpe_ratio': 0,
            'sortino_ratio': 0,
            'max_drawdown': 0,
            'avg_win': 0,
            'avg_loss': 0,
            'profit_loss_ratio': 0,
            'calmar_ratio': 0
        }
    
    def print_performance_summary(self, performance_report: Dict[str, Any]) -> None:
        """打印性能摘要"""
        print("\n📊 性能分析报告")
        print("=" * 60)
        
        portfolio = performance_report.get('portfolio_performance', {})
        
        print(f"📈 总体表现:")
        print(f"   总交易次数: {portfolio.get('total_trades', 0)}")
        print(f"   胜率: {portfolio.get('win_rate', 0):.2%}")
        print(f"   累计收益率: {portfolio.get('cumulative_return', 0):.2%}")
        print(f"   年化波动率: {portfolio.get('volatility', 0):.2%}")
        print(f"   夏普比率: {portfolio.get('sharpe_ratio', 0):.3f}")
        print(f"   最大回撤: {portfolio.get('max_drawdown', 0):.2%}")
        
        # 基准比较
        benchmark = performance_report.get('benchmark_comparison', {})
        if benchmark:
            print(f"\n📊 基准比较:")
            print(f"   Alpha: {benchmark.get('alpha', 0):.2%}")
            print(f"   Beta: {benchmark.get('beta', 0):.3f}")
            print(f"   信息比率: {benchmark.get('information_ratio', 0):.3f}")
        
        # 决策分析
        decisions = performance_report.get('decision_analysis', {})
        if decisions:
            print(f"\n🎯 决策分析:")
            for decision, stats in decisions.items():
                print(f"   {decision}: {stats['count']}次, 平均收益: {stats['avg_return']:.2%}, 胜率: {stats['win_rate']:.2%}")
        
        print("=" * 60)
