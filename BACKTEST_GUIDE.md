# TradingAgents 回测框架使用指南

## 📖 概述

TradingAgents回测框架是一个全面的金融交易策略回测系统，专为TradingAgents项目设计。它支持多模型配置、多代理选择、详细的性能分析和可视化报告生成。

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install pandas numpy matplotlib seaborn tqdm pyyaml
```

### 2. 快速测试

```bash
# 运行快速测试
python backtest_runner.py --quick-test

# 使用配置文件
python backtest_runner.py --config configs/default_config.json
```

### 3. 创建示例配置

```bash
python backtest_runner.py --create-configs
```

## 🏗️ 框架架构

### 核心组件

1. **配置管理** (`config.py`)
   - `BacktestConfig`: 主配置类
   - `ModelConfig`: 模型配置
   - `AgentConfig`: 代理配置

2. **执行引擎** (`engine.py`)
   - `BacktestEngine`: 回测执行引擎
   - 数据验证和批量处理
   - 错误处理和进度跟踪

3. **性能分析** (`performance.py`)
   - `PerformanceAnalyzer`: 性能分析器
   - 金融指标计算
   - 基准比较

4. **可视化** (`visualization.py`)
   - `ResultVisualizer`: 结果可视化器
   - 图表生成和报告导出

5. **实验管理** (`experiment.py`)
   - `ExperimentManager`: 实验管理器
   - 实验生命周期管理

## ⚙️ 配置系统

### 基本配置

```python
from tradingagents.backtesting import BacktestConfig, ModelConfig, AgentConfig

config = BacktestConfig(
    experiment_name="my_backtest",
    description="我的回测实验",
    start_date="2024-01-01",
    end_date="2024-03-31",
    symbols=["AAPL", "MSFT", "GOOGL"],
    
    # 模型配置
    model_config=ModelConfig(
        provider="deepseek",
        deep_think_model="deepseek-reasoner",
        quick_think_model="deepseek-chat",
        temperature=0.1
    ),
    
    # 代理配置
    agent_config=AgentConfig(
        selected_analysts=["market", "news", "fundamentals"],
        max_debate_rounds=1
    ),
    
    # 数据配置
    online_tools=False,
    output_dir="my_backtest_results"
)
```

### 配置文件格式

支持JSON和YAML格式：

```json
{
  "experiment_name": "deepseek_backtest",
  "description": "使用DeepSeek模型的回测",
  "start_date": "2024-01-01",
  "end_date": "2024-03-31",
  "symbols": ["AAPL", "MSFT"],
  "model_config": {
    "provider": "deepseek",
    "deep_think_model": "deepseek-reasoner",
    "quick_think_model": "deepseek-chat",
    "temperature": 0.1
  },
  "agent_config": {
    "selected_analysts": ["market", "news"],
    "max_debate_rounds": 1
  },
  "online_tools": false
}
```

## 🔧 使用方法

### 1. 编程接口

```python
from tradingagents.backtesting import BacktestEngine, ExperimentManager

# 创建实验管理器和引擎
experiment_manager = ExperimentManager()
engine = BacktestEngine(experiment_manager)

# 运行回测
result = engine.run_backtest(config)
experiment_id = result["experiment_id"]

print(f"回测完成: {experiment_id}")
```

### 2. 命令行接口

```bash
# 基本用法
python backtest_runner.py --config my_config.json

# 快速测试
python backtest_runner.py --quick-test

# 列出实验
python backtest_runner.py --list-experiments

# 不生成报告（仅执行回测）
python backtest_runner.py --config my_config.json --no-reports
```

### 3. 批量实验

```python
# 创建多个配置进行对比
configs = [
    BacktestConfig(experiment_name="low_temp", model_config=ModelConfig(temperature=0.1)),
    BacktestConfig(experiment_name="high_temp", model_config=ModelConfig(temperature=0.7))
]

results = []
for config in configs:
    result = engine.run_backtest(config)
    results.append(result)
```

## 📊 性能分析

### 支持的指标

- **收益指标**: 总收益、累计收益、平均收益
- **风险指标**: 波动率、下行波动率、最大回撤
- **风险调整收益**: 夏普比率、索提诺比率、卡尔马比率
- **交易统计**: 胜率、盈亏比、交易次数
- **基准比较**: Alpha、Beta、信息比率、跟踪误差

### 使用示例

```python
from tradingagents.backtesting import PerformanceAnalyzer

analyzer = PerformanceAnalyzer(risk_free_rate=0.02)

# 计算性能指标
performance = analyzer.calculate_portfolio_performance(returns_df)
print(f"夏普比率: {performance['sharpe_ratio']:.3f}")
print(f"最大回撤: {performance['max_drawdown']:.2%}")

# 生成完整报告
report = analyzer.generate_performance_report(returns_df, benchmark_data)
analyzer.print_performance_summary(report)
```

## 📈 可视化报告

### 自动生成的图表

1. **累计收益曲线**: 策略vs基准
2. **回撤曲线**: 时间序列回撤
3. **决策分布**: 买入/卖出/持有分布
4. **股票性能对比**: 各股票表现
5. **月度收益热力图**: 按月份的收益分布

### 自定义可视化

```python
from tradingagents.backtesting import ResultVisualizer

visualizer = ResultVisualizer(output_dir="my_charts")

# 生成单个图表
visualizer.plot_cumulative_returns(returns_df, benchmark_data)
visualizer.plot_drawdown(returns_df)

# 生成完整报告
visualizer.generate_comprehensive_report(
    returns_df, performance_report, benchmark_data, "output_dir"
)
```

## 🧪 实验管理

### 实验生命周期

1. **创建**: 定义配置并创建实验
2. **执行**: 运行回测分析
3. **分析**: 生成性能报告和可视化
4. **比较**: 对比不同实验结果
5. **归档**: 保存和管理历史实验

### 实验管理示例

```python
from tradingagents.backtesting import ExperimentManager

manager = ExperimentManager()

# 创建实验
experiment_id = manager.create_experiment(config)

# 查看实验
experiment = manager.get_experiment(experiment_id)
print(f"实验状态: {experiment['status']}")

# 列出所有实验
manager.print_experiment_summary()

# 导出实验摘要
summary_df = manager.export_experiment_summary("experiments.csv")
```

## 🎯 最佳实践

### 1. 配置管理

- 使用描述性的实验名称
- 保存配置文件以便重现实验
- 验证配置参数的合理性

### 2. 数据准备

- 确保数据文件存在且格式正确
- 检查日期范围的数据可用性
- 使用离线数据进行历史回测

### 3. 实验设计

- 从简单配置开始测试
- 逐步增加复杂性
- 使用对照实验验证结果

### 4. 结果分析

- 关注风险调整后的收益
- 分析决策分布的合理性
- 与基准进行对比

## 🔍 故障排除

### 常见问题

1. **数据文件不存在**
   ```
   解决方案: 检查data_dir配置，确保数据文件路径正确
   ```

2. **API密钥错误**
   ```
   解决方案: 设置DEEPSEEK_API_KEY环境变量
   ```

3. **内存不足**
   ```
   解决方案: 减少股票数量或缩短时间范围
   ```

4. **分析失败**
   ```
   解决方案: 检查网络连接，降低并发数量
   ```

### 调试模式

```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 使用debug模式
trading_graph = TradingAgentsGraph(debug=True, config=config)
```

## 📝 示例配置

### 快速测试配置

```json
{
  "experiment_name": "quick_test",
  "start_date": "2024-01-01",
  "end_date": "2024-01-31",
  "symbols": ["AAPL"],
  "agent_config": {
    "selected_analysts": ["market"]
  },
  "online_tools": false
}
```

### 全面分析配置

```json
{
  "experiment_name": "comprehensive_analysis",
  "start_date": "2024-01-01",
  "end_date": "2024-06-30",
  "symbols": ["AAPL", "MSFT", "GOOGL", "AMZN", "TSLA"],
  "agent_config": {
    "selected_analysts": ["market", "social", "news", "fundamentals"],
    "max_debate_rounds": 2
  },
  "online_tools": true
}
```

## 🤝 贡献指南

欢迎贡献代码和改进建议！请遵循以下步骤：

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

## 📄 许可证

本项目采用MIT许可证。详见LICENSE文件。
