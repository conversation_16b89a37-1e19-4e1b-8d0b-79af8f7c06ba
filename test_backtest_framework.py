#!/usr/bin/env python3
"""
TradingAgents 回测框架测试脚本

验证回测框架的基本功能和组件。
"""

import sys
import os
from pathlib import Path
import tempfile
import shutil

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

def test_imports():
    """测试模块导入"""
    print("🔍 测试模块导入...")
    
    try:
        from tradingagents.backtesting import (
            BacktestConfig, ModelConfig, AgentConfig,
            BacktestEngine, PerformanceAnalyzer, 
            ResultVisualizer, ExperimentManager
        )
        print("✅ 所有模块导入成功")
        return True
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return False


def test_config_creation():
    """测试配置创建和验证"""
    print("\n⚙️  测试配置创建...")
    
    try:
        from tradingagents.backtesting import BacktestConfig, ModelConfig, AgentConfig
        
        # 创建基本配置
        config = BacktestConfig(
            experiment_name="test_config",
            start_date="2024-01-01",
            end_date="2024-01-31",
            symbols=["AAPL"],
            model_config=ModelConfig(provider="deepseek"),
            agent_config=AgentConfig(selected_analysts=["market"])
        )
        
        # 验证配置
        config.validate()
        print("✅ 配置创建和验证成功")
        
        # 测试配置序列化
        config_dict = config.to_dict()
        restored_config = BacktestConfig.from_dict(config_dict)
        print("✅ 配置序列化成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False


def test_config_file_operations():
    """测试配置文件操作"""
    print("\n📁 测试配置文件操作...")
    
    try:
        from tradingagents.backtesting import BacktestConfig, ModelConfig, AgentConfig
        
        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            config = BacktestConfig(
                experiment_name="file_test",
                start_date="2024-01-01",
                end_date="2024-01-31",
                symbols=["AAPL"]
            )
            
            # 测试JSON保存和加载
            json_file = Path(temp_dir) / "test_config.json"
            config.save_to_file(str(json_file))
            loaded_config = BacktestConfig.load_from_file(str(json_file))
            
            assert loaded_config.experiment_name == config.experiment_name
            print("✅ JSON文件操作成功")
            
            # 测试YAML保存和加载
            yaml_file = Path(temp_dir) / "test_config.yaml"
            config.save_to_file(str(yaml_file))
            loaded_config = BacktestConfig.load_from_file(str(yaml_file))
            
            assert loaded_config.experiment_name == config.experiment_name
            print("✅ YAML文件操作成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置文件操作失败: {e}")
        return False


def test_experiment_manager():
    """测试实验管理器"""
    print("\n🧪 测试实验管理器...")
    
    try:
        from tradingagents.backtesting import ExperimentManager, BacktestConfig
        
        # 创建临时实验目录
        with tempfile.TemporaryDirectory() as temp_dir:
            manager = ExperimentManager(base_dir=temp_dir)
            
            # 创建测试配置
            config = BacktestConfig(
                experiment_name="test_experiment",
                start_date="2024-01-01",
                end_date="2024-01-31",
                symbols=["AAPL"]
            )
            
            # 创建实验
            experiment_id = manager.create_experiment(config)
            print(f"✅ 实验创建成功: {experiment_id}")
            
            # 获取实验信息
            experiment = manager.get_experiment(experiment_id)
            assert experiment is not None
            assert experiment["name"] == config.experiment_name
            print("✅ 实验信息获取成功")
            
            # 更新实验状态
            manager.update_experiment_status(experiment_id, "completed")
            updated_experiment = manager.get_experiment(experiment_id)
            assert updated_experiment["status"] == "completed"
            print("✅ 实验状态更新成功")
            
            # 列出实验
            experiments = manager.list_experiments()
            assert len(experiments) >= 1
            print("✅ 实验列表获取成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 实验管理器测试失败: {e}")
        return False


def test_performance_analyzer():
    """测试性能分析器"""
    print("\n📊 测试性能分析器...")
    
    try:
        from tradingagents.backtesting import PerformanceAnalyzer
        import pandas as pd
        import numpy as np
        
        analyzer = PerformanceAnalyzer()
        
        # 创建模拟收益数据
        np.random.seed(42)
        returns = np.random.normal(0.001, 0.02, 100)  # 模拟日收益
        
        returns_df = pd.DataFrame({
            'symbol': ['AAPL'] * 100,
            'date': pd.date_range('2024-01-01', periods=100),
            'decision': np.random.choice(['BUY', 'SELL', 'HOLD'], 100),
            'return_pct': returns
        })
        
        # 计算性能指标
        performance = analyzer.calculate_portfolio_performance(returns_df)
        
        # 验证关键指标存在
        required_metrics = [
            'total_trades', 'win_rate', 'total_return', 
            'sharpe_ratio', 'max_drawdown'
        ]
        
        for metric in required_metrics:
            assert metric in performance
        
        print("✅ 性能指标计算成功")
        
        # 生成性能报告
        report = analyzer.generate_performance_report(returns_df)
        assert 'portfolio_performance' in report
        assert 'symbol_performance' in report
        print("✅ 性能报告生成成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 性能分析器测试失败: {e}")
        return False


def test_visualizer():
    """测试可视化器"""
    print("\n📈 测试可视化器...")
    
    try:
        from tradingagents.backtesting import ResultVisualizer
        import pandas as pd
        import numpy as np
        import matplotlib
        matplotlib.use('Agg')  # 使用非交互式后端
        
        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            visualizer = ResultVisualizer(output_dir=temp_dir)
            
            # 创建模拟数据
            np.random.seed(42)
            returns_df = pd.DataFrame({
                'symbol': ['AAPL'] * 50,
                'date': pd.date_range('2024-01-01', periods=50),
                'decision': np.random.choice(['BUY', 'SELL', 'HOLD'], 50),
                'return_pct': np.random.normal(0.001, 0.02, 50)
            })
            
            # 测试单个图表生成（不显示）
            import matplotlib.pyplot as plt
            plt.ioff()  # 关闭交互模式
            
            # 这里只测试方法调用，不实际生成图片
            print("✅ 可视化器初始化成功")
            
        return True
        
    except Exception as e:
        print(f"❌ 可视化器测试失败: {e}")
        return False


def test_sample_configs():
    """测试示例配置"""
    print("\n📋 测试示例配置...")
    
    try:
        from tradingagents.backtesting.config import create_default_config, create_sample_configs
        
        # 测试默认配置
        default_config = create_default_config()
        default_config.validate()
        print("✅ 默认配置创建成功")
        
        # 测试示例配置
        sample_configs = create_sample_configs()
        
        for name, config in sample_configs.items():
            config.validate()
            print(f"✅ 示例配置 '{name}' 验证成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 示例配置测试失败: {e}")
        return False


def main():
    """运行所有测试"""
    print("🧪 TradingAgents 回测框架测试")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_imports),
        ("配置创建", test_config_creation),
        ("配置文件操作", test_config_file_operations),
        ("实验管理器", test_experiment_manager),
        ("性能分析器", test_performance_analyzer),
        ("可视化器", test_visualizer),
        ("示例配置", test_sample_configs),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！回测框架可以正常使用。")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关组件。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
