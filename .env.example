# TradingAgents Environment Configuration
# Copy this file to .env and fill in your API keys

# DeepSeek API (Primary LLM Provider - Recommended)
# Get your API key from: https://platform.deepseek.com/api_keys
DEEPSEEK_API_KEY=your_deepseek_api_key_here

# FinnHub API (Required for financial data)
# Get your free API key from: https://finnhub.io/register
FINNHUB_API_KEY=your_finnhub_api_key_here

# OpenAI API (Optional - Fallback LLM Provider)
# Get your API key from: https://platform.openai.com/api-keys
# OPENAI_API_KEY=your_openai_api_key_here

# Reddit API (Optional - For social sentiment analysis)
# Get credentials from: https://www.reddit.com/prefs/apps
# REDDIT_CLIENT_ID=your_reddit_client_id
# REDDIT_CLIENT_SECRET=your_reddit_client_secret
# REDDIT_USER_AGENT=TradingAgents/1.0

# Configuration Notes:
# - DeepSeek API is recommended for cost-effectiveness
# - Only DEEPSEEK_API_KEY and FINNHUB_API_KEY are required for basic functionality
# - OpenAI API can be used as a fallback if DeepSeek is not available
# - Reddit API is optional and only needed for social media sentiment analysis
