#!/usr/bin/env python3
"""
Reddit数据获取器测试脚本

快速测试Reddit数据获取功能是否正常工作
"""

import sys
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from scripts.reddit_data_fetcher import RedditDataFetcher
    print("✅ 成功导入Reddit数据获取器")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)


def test_initialization():
    """测试初始化"""
    print("\n🔧 测试初始化")
    print("=" * 40)
    
    try:
        fetcher = RedditDataFetcher(debug=True)
        print("✅ Reddit数据获取器初始化成功")
        
        # 检查属性
        print(f"📂 数据目录: {fetcher.data_dir}")
        print(f"📱 Reddit目录: {fetcher.reddit_data_dir}")
        print(f"📋 支持的subreddit: {len(fetcher.supported_subreddits)}")
        print(f"🏷️  股票映射: {len(fetcher.ticker_to_company)}")
        
        return fetcher
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        return None


def test_search_terms():
    """测试搜索关键词生成"""
    print("\n🔍 测试搜索关键词生成")
    print("=" * 40)
    
    fetcher = RedditDataFetcher(debug=False)
    
    test_cases = [
        "AAPL",
        "TSLA", 
        "META",
        "TSM",
        "UNKNOWN"  # 测试未知股票
    ]
    
    for ticker in test_cases:
        search_terms = fetcher.get_company_search_terms(ticker)
        print(f"{ticker:<8} -> {search_terms}")
    
    print("✅ 搜索关键词生成测试完成")


def test_data_structure():
    """测试数据结构创建"""
    print("\n📁 测试数据结构创建")
    print("=" * 40)
    
    fetcher = RedditDataFetcher(debug=True)
    
    # 检查目录是否创建
    reddit_dir = fetcher.reddit_data_dir
    company_news_dir = reddit_dir / "company_news"
    
    if company_news_dir.exists():
        print("✅ company_news目录存在")
        
        # 检查subreddit目录
        subreddit_count = 0
        file_count = 0
        
        for subreddit_dir in company_news_dir.iterdir():
            if subreddit_dir.is_dir():
                subreddit_count += 1
                jsonl_files = list(subreddit_dir.glob("*.jsonl"))
                file_count += len(jsonl_files)
                
                if jsonl_files:
                    print(f"  📂 {subreddit_dir.name}: {len(jsonl_files)} 文件")
        
        print(f"✅ 创建了 {subreddit_count} 个subreddit目录")
        print(f"✅ 创建了 {file_count} 个示例数据文件")
    else:
        print("❌ company_news目录不存在")


def test_basic_fetch():
    """测试基本数据获取"""
    print("\n📱 测试基本数据获取")
    print("=" * 40)
    
    fetcher = RedditDataFetcher(debug=True)
    
    # 测试获取AAPL数据
    ticker = "AAPL"
    date = "2024-05-20"
    
    print(f"测试获取 {ticker} 在 {date} 的数据...")
    
    results = fetcher.fetch_reddit_posts(
        ticker=ticker,
        target_date=date,
        days_back=1,
        max_posts_per_subreddit=2
    )
    
    if results.get('success'):
        print(f"✅ 获取成功")
        print(f"   总帖子数: {results.get('total_posts', 0)}")
        print(f"   处理时间: {results.get('processing_time', 0):.2f}秒")
        print(f"   subreddit分布: {results.get('subreddit_stats', {})}")
        
        # 检查帖子数据结构
        posts = results.get('posts', [])
        if posts:
            sample_post = posts[0]
            required_fields = ['title', 'content', 'upvotes', 'subreddit', 'posted_date']
            
            print(f"   示例帖子字段:")
            for field in required_fields:
                if field in sample_post:
                    print(f"     ✅ {field}: {type(sample_post[field]).__name__}")
                else:
                    print(f"     ❌ {field}: 缺失")
        
        return results
    else:
        print(f"❌ 获取失败: {results.get('error', 'Unknown error')}")
        return None


def test_multi_ticker():
    """测试多股票获取"""
    print("\n📊 测试多股票获取")
    print("=" * 40)
    
    fetcher = RedditDataFetcher(debug=False)
    
    tickers = ["AAPL", "TSLA", "NVDA"]
    date = "2024-05-20"
    
    results_summary = []
    
    for ticker in tickers:
        print(f"测试 {ticker}...")
        
        results = fetcher.fetch_reddit_posts(
            ticker=ticker,
            target_date=date,
            days_back=1,
            max_posts_per_subreddit=1
        )
        
        if results.get('success'):
            total_posts = results.get('total_posts', 0)
            processing_time = results.get('processing_time', 0)
            
            results_summary.append({
                'ticker': ticker,
                'posts': total_posts,
                'time': processing_time
            })
            
            print(f"  ✅ {ticker}: {total_posts} 帖子 ({processing_time:.2f}秒)")
        else:
            print(f"  ❌ {ticker}: 失败")
    
    print(f"\n📋 多股票测试汇总:")
    for summary in results_summary:
        print(f"  {summary['ticker']}: {summary['posts']} 帖子, {summary['time']:.2f}秒")
    
    return results_summary


def test_sentiment_analysis():
    """测试情绪分析"""
    print("\n📈 测试情绪分析")
    print("=" * 40)
    
    fetcher = RedditDataFetcher(debug=False)
    
    # 获取数据
    results = fetcher.fetch_reddit_posts(
        ticker="AAPL",
        target_date="2024-05-20",
        days_back=1,
        max_posts_per_subreddit=3
    )
    
    if results.get('success'):
        sentiment = fetcher.get_sentiment_summary(results)
        
        print("✅ 情绪分析完成:")
        print(f"   总帖子数: {sentiment['total_posts']}")
        print(f"   正面帖子: {sentiment['positive_posts']}")
        print(f"   负面帖子: {sentiment['negative_posts']}")
        print(f"   中性帖子: {sentiment['neutral_posts']}")
        print(f"   整体情绪: {sentiment['overall_sentiment']}")
        print(f"   平均赞数: {sentiment['average_upvotes']:.1f}")
        
        return sentiment
    else:
        print("❌ 无法进行情绪分析，数据获取失败")
        return None


def test_save_functionality():
    """测试保存功能"""
    print("\n💾 测试保存功能")
    print("=" * 40)
    
    fetcher = RedditDataFetcher(debug=False)
    
    # 获取数据
    results = fetcher.fetch_reddit_posts(
        ticker="AAPL",
        target_date="2024-05-20",
        days_back=1,
        max_posts_per_subreddit=2
    )
    
    if results.get('success'):
        try:
            # 保存结果
            filepath = fetcher.save_results(results)
            
            print(f"✅ 保存成功: {filepath}")
            
            # 验证文件
            if filepath.exists():
                file_size = filepath.stat().st_size
                print(f"📄 JSON文件大小: {file_size} 字节")
                
                # 检查对应的文本文件
                txt_file = filepath.with_suffix('.txt')
                if txt_file.exists():
                    txt_size = txt_file.stat().st_size
                    print(f"📄 文本文件大小: {txt_size} 字节")
                
                # 验证JSON内容
                import json
                with open(filepath, 'r', encoding='utf-8') as f:
                    loaded_data = json.load(f)
                
                print(f"✅ JSON验证: 包含 {loaded_data.get('total_posts', 0)} 个帖子")
                
                return filepath
            else:
                print("❌ 文件不存在")
                return None
                
        except Exception as e:
            print(f"❌ 保存失败: {e}")
            return None
    else:
        print("❌ 无法保存，数据获取失败")
        return None


def test_error_handling():
    """测试错误处理"""
    print("\n⚠️ 测试错误处理")
    print("=" * 40)
    
    fetcher = RedditDataFetcher(debug=False)
    
    # 测试无效日期
    print("测试无效日期...")
    try:
        results = fetcher.fetch_reddit_posts(
            ticker="AAPL",
            target_date="invalid-date",
            days_back=1
        )
        print("❌ 应该抛出异常但没有")
    except Exception as e:
        print(f"✅ 正确处理无效日期: {type(e).__name__}")
    
    # 测试不存在的数据目录
    print("测试不存在的数据目录...")
    temp_fetcher = RedditDataFetcher(data_dir="/nonexistent/path", debug=False)
    results = temp_fetcher.fetch_reddit_posts(
        ticker="AAPL",
        target_date="2024-05-20",
        days_back=1
    )
    
    if not results.get('success'):
        print(f"✅ 正确处理不存在的目录: {results.get('error', '')[:50]}...")
    else:
        print("❌ 应该失败但成功了")


def main():
    """主测试函数"""
    print("🧪 Reddit数据获取器测试")
    print("=" * 50)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 1. 测试初始化
        fetcher = test_initialization()
        if not fetcher:
            print("❌ 初始化失败，终止测试")
            return
        
        # 2. 测试搜索关键词
        test_search_terms()
        
        # 3. 测试数据结构
        test_data_structure()
        
        # 4. 测试基本获取
        basic_results = test_basic_fetch()
        
        # 5. 测试多股票获取
        multi_results = test_multi_ticker()
        
        # 6. 测试情绪分析
        sentiment_results = test_sentiment_analysis()
        
        # 7. 测试保存功能
        save_results = test_save_functionality()
        
        # 8. 测试错误处理
        test_error_handling()
        
        print(f"\n🎯 测试完成!")
        print("=" * 50)
        
        # 提供使用建议
        print("\n💡 使用建议:")
        print("✅ Reddit数据获取器功能正常")
        print("📁 示例数据结构已创建，可以开始使用")
        print("🚀 命令行使用: python scripts/reddit_data_fetcher.py --help")
        print("📖 示例代码: python examples/reddit_data_example.py")
        
        if basic_results and basic_results.get('total_posts', 0) > 0:
            print("✅ 数据获取功能正常，找到了示例帖子")
        else:
            print("⚠️ 未找到帖子数据，可能需要添加真实的Reddit数据文件")
        
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
