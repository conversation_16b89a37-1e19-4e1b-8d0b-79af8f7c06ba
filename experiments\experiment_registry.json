{"7adb53ed": {"id": "7adb53ed", "name": "quick_test", "description": "快速测试配置 - 单股票，短时间", "created_at": "2025-06-23T18:53:47.752624", "status": "failed", "config_path": "experiments\\7adb53ed\\config.json", "directory": "experiments\\7adb53ed", "start_date": "2024-01-01", "end_date": "2024-01-03", "symbols": ["AAPL"], "model_provider": "deepseek", "analysts": ["market"], "updated_at": "2025-06-23T18:53:47.754724", "error": "没有可用的股票数据", "traceback": "Traceback (most recent call last):\n  File \"D:\\vscode_project\\TradingAgents\\tradingagents\\backtesting\\engine.py\", line 247, in run_backtest\n    raise ValueError(\"没有可用的股票数据\")\nValueError: 没有可用的股票数据\n"}, "947b3d41": {"id": "947b3d41", "name": "quick_test", "description": "快速测试配置 - 单股票，短时间", "created_at": "2025-06-23T18:58:45.366572", "status": "failed", "config_path": "experiments\\947b3d41\\config.json", "directory": "experiments\\947b3d41", "start_date": "2024-01-01", "end_date": "2024-01-03", "symbols": ["AAPL"], "model_provider": "deepseek", "analysts": ["market"], "updated_at": "2025-06-23T18:58:45.393713", "error": "没有可用的股票数据", "traceback": "Traceback (most recent call last):\n  File \"D:\\vscode_project\\TradingAgents\\tradingagents\\backtesting\\engine.py\", line 247, in run_backtest\n    raise ValueError(\"没有可用的股票数据\")\nValueError: 没有可用的股票数据\n"}, "6b429b36": {"id": "6b429b36", "name": "quick_test", "description": "快速测试配置 - 单股票，短时间", "created_at": "2025-06-23T18:59:14.995981", "status": "failed", "config_path": "experiments\\6b429b36\\config.json", "directory": "experiments\\6b429b36", "start_date": "2024-01-01", "end_date": "2024-01-03", "symbols": ["AAPL"], "model_provider": "deepseek", "analysts": ["market"], "updated_at": "2025-06-23T19:05:12.999410", "completed_at": "2025-06-23T19:05:12.997480", "results_file": "experiments\\6b429b36\\results\\summary.json", "total_trades": 0, "total_return": 0, "sharpe_ratio": 0, "error": "'success_rate'", "traceback": "Traceback (most recent call last):\n  File \"D:\\vscode_project\\TradingAgents\\tradingagents\\backtesting\\engine.py\", line 308, in run_backtest\n    self.logger.info(f\"   成功率: {summary['success_rate']:.2%}\")\n                                   ~~~~~~~^^^^^^^^^^^^^^^^\nKeyError: 'success_rate'\n"}, "795746e4": {"id": "795746e4", "name": "quick_test", "description": "快速测试配置 - 单股票，短时间", "created_at": "2025-06-23T19:32:11.686118", "status": "completed", "config_path": "experiments\\795746e4\\config.json", "directory": "experiments\\795746e4", "start_date": "2024-01-01", "end_date": "2024-01-03", "symbols": ["AAPL"], "model_provider": "deepseek", "analysts": ["market"], "updated_at": "2025-06-23T19:38:15.914110", "completed_at": "2025-06-23T19:38:15.914099", "results_file": "experiments\\795746e4\\results\\summary.json", "total_trades": 0, "total_return": 0, "sharpe_ratio": 0}}