"""
实验管理模块

管理回测实验的生命周期，包括实验创建、执行跟踪、结果保存等。
"""

import os
import json
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path
import pandas as pd

from .config import BacktestConfig


class ExperimentManager:
    """实验管理器"""
    
    def __init__(self, base_dir: str = "experiments"):
        """
        初始化实验管理器
        
        Args:
            base_dir: 实验基础目录
        """
        self.base_dir = Path(base_dir)
        self.base_dir.mkdir(parents=True, exist_ok=True)
        
        # 实验注册表文件
        self.registry_file = self.base_dir / "experiment_registry.json"
        self.experiments = self._load_registry()
    
    def _load_registry(self) -> Dict[str, Dict]:
        """加载实验注册表"""
        if self.registry_file.exists():
            with open(self.registry_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {}
    
    def _save_registry(self) -> None:
        """保存实验注册表"""
        with open(self.registry_file, 'w', encoding='utf-8') as f:
            json.dump(self.experiments, f, indent=2, ensure_ascii=False)
    
    def create_experiment(self, config: BacktestConfig) -> str:
        """
        创建新实验
        
        Args:
            config: 回测配置
            
        Returns:
            实验ID
        """
        # 生成唯一实验ID
        experiment_id = str(uuid.uuid4())[:8]
        
        # 创建实验目录
        exp_dir = self.base_dir / experiment_id
        exp_dir.mkdir(parents=True, exist_ok=True)
        
        # 保存配置
        config_path = exp_dir / "config.json"
        config.save_to_file(str(config_path))
        
        # 创建子目录
        (exp_dir / "results").mkdir(exist_ok=True)
        (exp_dir / "logs").mkdir(exist_ok=True)
        (exp_dir / "reports").mkdir(exist_ok=True)
        (exp_dir / "charts").mkdir(exist_ok=True)
        
        # 注册实验
        self.experiments[experiment_id] = {
            "id": experiment_id,
            "name": config.experiment_name,
            "description": config.description,
            "created_at": datetime.now().isoformat(),
            "status": "created",
            "config_path": str(config_path),
            "directory": str(exp_dir),
            "start_date": config.start_date,
            "end_date": config.end_date,
            "symbols": config.symbols,
            "model_provider": config.model_config.provider,
            "analysts": config.agent_config.selected_analysts
        }
        
        self._save_registry()
        
        print(f"✅ 实验已创建: {experiment_id}")
        print(f"   名称: {config.experiment_name}")
        print(f"   目录: {exp_dir}")
        
        return experiment_id
    
    def get_experiment(self, experiment_id: str) -> Optional[Dict]:
        """获取实验信息"""
        return self.experiments.get(experiment_id)
    
    def list_experiments(self, status: Optional[str] = None) -> List[Dict]:
        """
        列出实验
        
        Args:
            status: 过滤状态 ("created", "running", "completed", "failed")
        """
        experiments = list(self.experiments.values())
        
        if status:
            experiments = [exp for exp in experiments if exp["status"] == status]
        
        # 按创建时间排序
        experiments.sort(key=lambda x: x["created_at"], reverse=True)
        
        return experiments
    
    def update_experiment_status(self, experiment_id: str, status: str, 
                                metadata: Optional[Dict] = None) -> None:
        """
        更新实验状态
        
        Args:
            experiment_id: 实验ID
            status: 新状态
            metadata: 额外元数据
        """
        if experiment_id not in self.experiments:
            raise ValueError(f"实验不存在: {experiment_id}")
        
        self.experiments[experiment_id]["status"] = status
        self.experiments[experiment_id]["updated_at"] = datetime.now().isoformat()
        
        if metadata:
            self.experiments[experiment_id].update(metadata)
        
        self._save_registry()
    
    def get_experiment_directory(self, experiment_id: str) -> Path:
        """获取实验目录"""
        if experiment_id not in self.experiments:
            raise ValueError(f"实验不存在: {experiment_id}")
        
        return Path(self.experiments[experiment_id]["directory"])
    
    def load_experiment_config(self, experiment_id: str) -> BacktestConfig:
        """加载实验配置"""
        if experiment_id not in self.experiments:
            raise ValueError(f"实验不存在: {experiment_id}")
        
        config_path = self.experiments[experiment_id]["config_path"]
        return BacktestConfig.load_from_file(config_path)
    
    def save_experiment_results(self, experiment_id: str, results: Dict[str, Any]) -> None:
        """
        保存实验结果
        
        Args:
            experiment_id: 实验ID
            results: 结果数据
        """
        exp_dir = self.get_experiment_directory(experiment_id)
        results_file = exp_dir / "results" / "summary.json"
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False, default=str)
        
        # 更新实验元数据
        self.update_experiment_status(
            experiment_id, 
            "completed",
            {
                "completed_at": datetime.now().isoformat(),
                "results_file": str(results_file),
                "total_trades": results.get("total_trades", 0),
                "total_return": results.get("performance", {}).get("total_return", 0),
                "sharpe_ratio": results.get("performance", {}).get("sharpe_ratio", 0)
            }
        )
    
    def delete_experiment(self, experiment_id: str, confirm: bool = False) -> None:
        """
        删除实验
        
        Args:
            experiment_id: 实验ID
            confirm: 确认删除
        """
        if not confirm:
            raise ValueError("删除实验需要确认参数 confirm=True")
        
        if experiment_id not in self.experiments:
            raise ValueError(f"实验不存在: {experiment_id}")
        
        # 删除实验目录
        exp_dir = self.get_experiment_directory(experiment_id)
        if exp_dir.exists():
            import shutil
            shutil.rmtree(exp_dir)
        
        # 从注册表中删除
        del self.experiments[experiment_id]
        self._save_registry()
        
        print(f"✅ 实验已删除: {experiment_id}")
    
    def export_experiment_summary(self, output_file: str = None) -> pd.DataFrame:
        """
        导出实验摘要
        
        Args:
            output_file: 输出文件路径
            
        Returns:
            实验摘要DataFrame
        """
        if not self.experiments:
            return pd.DataFrame()
        
        # 转换为DataFrame
        df = pd.DataFrame(list(self.experiments.values()))
        
        # 选择关键列
        columns = [
            "id", "name", "status", "created_at", "start_date", "end_date",
            "model_provider", "total_trades", "total_return", "sharpe_ratio"
        ]
        
        # 只保留存在的列
        available_columns = [col for col in columns if col in df.columns]
        df = df[available_columns]
        
        if output_file:
            df.to_csv(output_file, index=False, encoding='utf-8')
            print(f"✅ 实验摘要已导出: {output_file}")
        
        return df
    
    def print_experiment_summary(self) -> None:
        """打印实验摘要"""
        if not self.experiments:
            print("📝 暂无实验")
            return
        
        print(f"\n📊 实验摘要 (共 {len(self.experiments)} 个实验)")
        print("=" * 80)
        
        for exp_id, exp in self.experiments.items():
            status_emoji = {
                "created": "🆕",
                "running": "🔄", 
                "completed": "✅",
                "failed": "❌"
            }.get(exp["status"], "❓")
            
            print(f"{status_emoji} {exp_id[:8]} | {exp['name']}")
            print(f"   状态: {exp['status']} | 创建: {exp['created_at'][:10]}")
            print(f"   时间: {exp['start_date']} ~ {exp['end_date']}")
            print(f"   股票: {', '.join(exp['symbols'][:3])}{'...' if len(exp['symbols']) > 3 else ''}")
            
            if exp["status"] == "completed":
                total_return = exp.get("total_return", "N/A")
                sharpe = exp.get("sharpe_ratio", "N/A")
                print(f"   收益: {total_return} | 夏普: {sharpe}")
            
            print()
