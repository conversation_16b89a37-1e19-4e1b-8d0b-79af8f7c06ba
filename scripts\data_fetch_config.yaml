# 在线数据获取配置文件
# 用于批量获取多个时间段和股票的数据

# 基本配置
data_dir: "data"  # 数据保存目录
delay: 3.0        # API调用间隔(秒) - 增加到3秒避免速率限制
verbose: true     # 是否显示详细输出

# API调用配置
api_settings:
  max_retries: 3          # 最大重试次数
  retry_delay: 5.0        # 重试间隔(秒)
  rate_limit_delay: 120   # 速率限制时的等待时间(秒)

# 股票配置
symbols:
  # 科技股
  - "AAPL"   # 苹果
  - "MSFT"   # 微软
  - "GOOGL"  # 谷歌
  - "AMZN"   # 亚马逊
  - "TSLA"   # 特斯拉
  
  # 金融股
  - "JPM"    # 摩根大通
  - "BAC"    # 美国银行
  - "WFC"    # 富国银行
  
  # 医疗股
  - "JNJ"    # 强生
  - "PFE"    # 辉瑞

# 时间段配置
date_ranges:
  # 2024年第一季度
  - start_date: "2024-01-01"
    end_date: "2024-03-31"
    name: "2024_Q1"
  
  # 2024年第二季度
  - start_date: "2024-04-01"
    end_date: "2024-06-30"
    name: "2024_Q2"
  
  # 2024年第三季度
  - start_date: "2024-07-01"
    end_date: "2024-09-30"
    name: "2024_Q3"
  
  # 2024年第四季度
  - start_date: "2024-10-01"
    end_date: "2024-12-31"
    name: "2024_Q4"

# 分析师配置
analysts:
  # 可选值: market, news, social, fundamentals
  enabled:
    - "market"        # 市场分析师
    - "news"          # 新闻分析师
    - "social"        # 社交媒体分析师
    - "fundamentals"  # 基本面分析师
  
  # 各分析师的特定配置
  market:
    indicators:
      - "rsi_14"      # 14日RSI
      - "macd"        # MACD
      - "boll"        # 布林带
      - "ma_20"       # 20日移动平均
      - "ema_12"      # 12日指数移动平均
      - "ma_50"       # 50日移动平均
      - "ma_200"      # 200日移动平均
    
  news:
    sources:
      - "global"      # 全球新闻
      - "google"      # Google新闻
    
  social:
    platforms:
      - "openai"      # OpenAI社交媒体搜索
    
  fundamentals:
    metrics:
      - "pe_ratio"    # 市盈率
      - "ps_ratio"    # 市销率
      - "cash_flow"   # 现金流
      - "debt_ratio"  # 负债比率

# API配置
api:
  # 重试配置
  max_retries: 3
  retry_delay: 2.0
  
  # 超时配置
  timeout: 30
  
  # 速率限制
  rate_limit:
    calls_per_minute: 50
    calls_per_hour: 1000

# 数据质量配置
data_quality:
  # 验证配置
  validate_data: true
  
  # 最小数据量要求
  min_data_points: 10
  
  # 数据完整性检查
  check_completeness: true

# 输出配置
output:
  # 文件格式
  formats:
    - "json"        # JSON格式
    - "csv"         # CSV格式(适用于数值数据)
    - "txt"         # 文本格式(适用于文本数据)
  
  # 压缩配置
  compress: false
  compression_format: "zip"
  
  # 备份配置
  create_backup: true
  backup_dir: "backups"

# 日志配置
logging:
  level: "INFO"     # DEBUG, INFO, WARNING, ERROR
  file: "logs/data_fetch.log"
  max_size: "10MB"
  backup_count: 5

# 通知配置
notifications:
  # 邮件通知(可选)
  email:
    enabled: false
    smtp_server: "smtp.gmail.com"
    smtp_port: 587
    username: "<EMAIL>"
    password: "your_app_password"
    recipients:
      - "<EMAIL>"
  
  # 完成后通知
  on_completion: true
  on_error: true
