#!/usr/bin/env python3
"""
Example usage of TradingAgents with DeepSeek models.
This script demonstrates how to use different DeepSeek models for trading analysis.
"""

import sys
import os
from datetime import datetime, timedelta

# Add parent directory to path to import tradingagents
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tradingagents.graph.trading_graph import TradingAgentsGraph
from tradingagents.default_config import DEFAULT_CONFIG


def example_basic_analysis():
    """Basic trading analysis example with DeepSeek."""
    print("🔍 Basic Trading Analysis with DeepSeek")
    print("=" * 50)
    
    # Create configuration for DeepSeek
    config = DEFAULT_CONFIG.copy()
    config["llm_provider"] = "deepseek"
    config["deep_think_llm"] = "deepseek-reasoner"  # For complex reasoning
    config["quick_think_llm"] = "deepseek-chat"     # For fast analysis
    config["max_debate_rounds"] = 1                 # Light analysis
    config["online_tools"] = True                   # Use real-time data
    
    # Initialize TradingAgents
    ta = TradingAgentsGraph(
        selected_analysts=["market", "news"],  # Use market and news analysts
        debug=True,
        config=config
    )
    
    # Run analysis for Apple stock
    ticker = "AAPL"
    date = "2024-05-10"
    
    print(f"📊 Analyzing {ticker} for {date}...")
    
    try:
        final_state, decision = ta.propagate(ticker, date)
        
        print(f"\n✅ Analysis Complete!")
        print(f"🎯 Trading Decision: {decision}")
        
        # Print key reports
        if "market_report" in final_state:
            print(f"\n📈 Market Analysis:")
            print(final_state["market_report"][:300] + "...")
        
        if "news_report" in final_state:
            print(f"\n📰 News Analysis:")
            print(final_state["news_report"][:300] + "...")
            
        return True
        
    except Exception as e:
        print(f"❌ Analysis failed: {str(e)}")
        return False


def example_comprehensive_analysis():
    """Comprehensive trading analysis with all analysts."""
    print("\n🔬 Comprehensive Trading Analysis with DeepSeek")
    print("=" * 50)
    
    # Create configuration for comprehensive analysis
    config = DEFAULT_CONFIG.copy()
    config["llm_provider"] = "deepseek"
    config["deep_think_llm"] = "deepseek-reasoner"  # Advanced reasoning
    config["quick_think_llm"] = "deepseek-chat"     # Fast analysis
    config["max_debate_rounds"] = 2                 # More thorough debate
    config["max_risk_discuss_rounds"] = 2           # Detailed risk analysis
    config["online_tools"] = True
    
    # Initialize with all analysts
    ta = TradingAgentsGraph(
        selected_analysts=["market", "social", "news", "fundamentals"],
        debug=True,
        config=config
    )
    
    # Analyze NVIDIA stock
    ticker = "NVDA"
    date = "2024-06-15"
    
    print(f"📊 Comprehensive analysis of {ticker} for {date}...")
    
    try:
        final_state, decision = ta.propagate(ticker, date)
        
        print(f"\n✅ Comprehensive Analysis Complete!")
        print(f"🎯 Final Trading Decision: {decision}")
        
        # Print summary of all reports
        reports = [
            ("Market", "market_report"),
            ("Social", "sentiment_report"), 
            ("News", "news_report"),
            ("Fundamentals", "fundamentals_report"),
            ("Investment Plan", "investment_plan"),
            ("Trader Decision", "trader_investment_plan"),
            ("Final Decision", "final_trade_decision")
        ]
        
        for name, key in reports:
            if key in final_state and final_state[key]:
                print(f"\n📋 {name} Summary:")
                content = str(final_state[key])
                print(content[:200] + "..." if len(content) > 200 else content)
        
        return True
        
    except Exception as e:
        print(f"❌ Comprehensive analysis failed: {str(e)}")
        return False


def example_model_comparison():
    """Compare different DeepSeek models."""
    print("\n🆚 DeepSeek Model Comparison")
    print("=" * 50)
    
    models = [
        ("deepseek-chat", "General purpose chat model"),
        ("deepseek-coder", "Code-specialized model"),
        ("deepseek-reasoner", "Advanced reasoning model")
    ]
    
    ticker = "TSLA"
    date = "2024-05-20"
    
    for model, description in models:
        print(f"\n🤖 Testing {model} ({description})")
        print("-" * 40)
        
        try:
            config = DEFAULT_CONFIG.copy()
            config["llm_provider"] = "deepseek"
            config["deep_think_llm"] = model
            config["quick_think_llm"] = model
            config["max_debate_rounds"] = 1
            config["online_tools"] = False  # Use cached data for speed
            
            ta = TradingAgentsGraph(
                selected_analysts=["market"],  # Just market analysis for speed
                debug=False,  # Reduce output
                config=config
            )
            
            final_state, decision = ta.propagate(ticker, date)
            print(f"✅ {model}: {decision}")
            
        except Exception as e:
            print(f"❌ {model} failed: {str(e)}")


def example_cost_optimization():
    """Example of cost-optimized configuration."""
    print("\n💰 Cost-Optimized Analysis")
    print("=" * 50)
    
    # Minimal cost configuration
    config = DEFAULT_CONFIG.copy()
    config["llm_provider"] = "deepseek"
    config["deep_think_llm"] = "deepseek-chat"      # Use same model for both
    config["quick_think_llm"] = "deepseek-chat"     # to minimize costs
    config["max_debate_rounds"] = 1                 # Minimal debate
    config["max_risk_discuss_rounds"] = 1           # Minimal risk discussion
    config["online_tools"] = False                  # Use cached data
    
    ta = TradingAgentsGraph(
        selected_analysts=["market"],  # Single analyst
        debug=True,
        config=config
    )
    
    ticker = "SPY"
    date = "2024-04-15"
    
    print(f"📊 Cost-optimized analysis of {ticker} for {date}...")
    
    try:
        final_state, decision = ta.propagate(ticker, date)
        print(f"✅ Cost-optimized analysis complete: {decision}")
        return True
    except Exception as e:
        print(f"❌ Cost-optimized analysis failed: {str(e)}")
        return False


def main():
    """Run all examples."""
    print("🚀 TradingAgents DeepSeek Examples")
    print("=" * 60)
    
    examples = [
        ("Basic Analysis", example_basic_analysis),
        ("Comprehensive Analysis", example_comprehensive_analysis),
        ("Model Comparison", example_model_comparison),
        ("Cost Optimization", example_cost_optimization),
    ]
    
    for name, func in examples:
        print(f"\n🎯 Running {name}...")
        try:
            success = func()
            if success:
                print(f"✅ {name} completed successfully")
            else:
                print(f"⚠️ {name} completed with issues")
        except Exception as e:
            print(f"❌ {name} failed: {str(e)}")
        
        print("\n" + "="*60)
    
    print("\n🎉 All examples completed!")
    print("\nFor more information:")
    print("- Check README.md for detailed documentation")
    print("- Run 'python -m cli.main' for interactive analysis")


if __name__ == "__main__":
    main()
