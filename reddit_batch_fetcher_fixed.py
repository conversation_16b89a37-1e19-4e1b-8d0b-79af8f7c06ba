#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Reddit股票数据批量获取脚本 - 编码修复版

专门用于获取AAPL、MSFT、NVDA三只股票从2025.01.01-2025.06.25期间的真实Reddit帖子数据。
修复了Windows系统下的编码问题。

使用方法：
python reddit_batch_fetcher_fixed.py
"""

import os
import sys
import io

# 修复Windows编码问题
if sys.platform == 'win32':
    # 设置环境变量强制使用UTF-8
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    
    # 重新包装stdout和stderr
    if hasattr(sys.stdout, 'buffer'):
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')
    if hasattr(sys.stderr, 'buffer'):
        sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8', errors='replace')

import praw
import json
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional
import logging
from collections import defaultdict
import re


class RedditBatchFetcherFixed:
    """Reddit批量数据获取器 - 编码修复版"""
    
    def __init__(self):
        """初始化批量获取器"""
        # 设置日志 - 使用安全的编码
        self.setup_logging()
        
        # 初始化Reddit API
        self.setup_reddit_api()
        
        # 目标股票配置
        self.target_stocks = {
            'AAPL': {
                'name': 'Apple',
                'keywords': ['AAPL', 'Apple', 'iPhone', 'iPad', 'Mac', 'iOS'],
                'folder': 'AAPL_Reddit'
            },
            'MSFT': {
                'name': 'Microsoft',
                'keywords': ['MSFT', 'Microsoft', 'Windows', 'Azure', 'Office', 'Xbox'],
                'folder': 'MSFT_Reddit'
            },
            'NVDA': {
                'name': 'Nvidia',
                'keywords': ['NVDA', 'Nvidia', 'GPU', 'GeForce', 'RTX', 'AI chip'],
                'folder': 'NVDA_Reddit'
            }
        }
        
        # 时间范围配置
        self.start_date = datetime(2025, 1, 1)
        self.end_date = datetime(2025, 6, 25)
        
        # 目标subreddit列表
        self.subreddits = [
            'stocks', 'investing', 'wallstreetbets', 'SecurityAnalysis',
            'ValueInvesting', 'StockMarket', 'options', 'dividends',
            'financialindependence', 'pennystocks'
        ]
        
        # 每日最小帖子数
        self.min_posts_per_day = 10
        
        # 情绪分析关键词
        self.sentiment_keywords = {
            'positive': [
                'bullish', 'buy', 'moon', 'rocket', 'strong', 'good', 'great',
                'excellent', 'positive', 'up', 'rise', 'gain', 'profit', 'bull',
                'long', 'hold', 'diamond hands', 'to the moon', 'calls', 'growth',
                'outperform', 'beat', 'exceed', 'solid', 'impressive', 'surge'
            ],
            'negative': [
                'bearish', 'sell', 'short', 'weak', 'bad', 'terrible',
                'negative', 'down', 'fall', 'loss', 'crash', 'bear',
                'puts', 'dump', 'overvalued', 'bubble', 'decline', 'drop',
                'underperform', 'miss', 'disappointing', 'concern', 'risk'
            ]
        }
    
    def setup_logging(self):
        """设置日志系统 - 安全编码版本"""
        # 创建日志格式器
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        
        # 创建文件处理器
        file_handler = logging.FileHandler('reddit_batch_fetcher.log', encoding='utf-8')
        file_handler.setFormatter(formatter)
        
        # 创建控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        
        # 配置根日志器
        self.logger = logging.getLogger(__name__)
        self.logger.setLevel(logging.INFO)
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
    
    def setup_reddit_api(self):
        """设置Reddit API连接"""
        try:
            self.reddit = praw.Reddit(
                client_id="vyg0MLjDgpqYQRNHaJaJEQ",
                client_secret="hy412_1dYvB0xXKe5tUWYCTSeFZpGQ",
                user_agent="ai_fund_hedge by /u/Available_Neck_1936"
            )
            
            # 测试连接
            self.reddit.user.me()
            self.logger.info("[SUCCESS] Reddit API连接成功")
            
        except Exception as e:
            self.logger.error(f"[ERROR] Reddit API连接失败: {e}")
            raise
    
    def create_folder_structure(self):
        """创建文件夹结构"""
        self.logger.info("[SETUP] 创建文件夹结构...")
        
        for ticker, config in self.target_stocks.items():
            folder_path = Path(config['folder'])
            folder_path.mkdir(exist_ok=True)
            self.logger.info(f"   创建文件夹: {folder_path}")
    
    def get_date_range(self) -> List[datetime]:
        """获取日期范围列表"""
        dates = []
        current_date = self.start_date
        
        while current_date <= self.end_date:
            dates.append(current_date)
            current_date += timedelta(days=1)
        
        return dates
    
    def calculate_sentiment_score(self, text: str) -> Dict[str, Any]:
        """计算情绪得分"""
        text_lower = text.lower()
        
        # 计算正面和负面关键词数量
        positive_count = sum(1 for keyword in self.sentiment_keywords['positive'] 
                           if keyword in text_lower)
        negative_count = sum(1 for keyword in self.sentiment_keywords['negative'] 
                           if keyword in text_lower)
        
        # 计算情绪得分 (-1 到 1)
        total_keywords = positive_count + negative_count
        if total_keywords == 0:
            sentiment_score = 0.0
            sentiment_label = 'neutral'
        else:
            sentiment_score = (positive_count - negative_count) / total_keywords
            if sentiment_score > 0.2:
                sentiment_label = 'positive'
            elif sentiment_score < -0.2:
                sentiment_label = 'negative'
            else:
                sentiment_label = 'neutral'
        
        return {
            'score': round(sentiment_score, 3),
            'label': sentiment_label,
            'positive_keywords': positive_count,
            'negative_keywords': negative_count,
            'total_keywords': total_keywords
        }
    
    def is_relevant_post(self, post, keywords: List[str]) -> bool:
        """检查帖子是否与股票相关"""
        title = post.title.lower()
        content = post.selftext.lower()
        
        # 检查是否包含任何关键词
        for keyword in keywords:
            if keyword.lower() in title or keyword.lower() in content:
                return True
        
        return False
    
    def fetch_posts_for_date(self, ticker: str, target_date: datetime) -> List[Dict]:
        """获取指定日期的帖子"""
        config = self.target_stocks[ticker]
        keywords = config['keywords']
        
        self.logger.info(f"   [FETCH] 获取 {ticker} 在 {target_date.strftime('%Y-%m-%d')} 的帖子...")
        
        all_posts = []
        
        # 遍历每个subreddit
        for subreddit_name in self.subreddits:
            try:
                subreddit = self.reddit.subreddit(subreddit_name)
                
                # 搜索相关帖子
                for keyword in keywords[:2]:  # 限制关键词数量避免API限制
                    try:
                        search_results = subreddit.search(
                            keyword, 
                            time_filter="all",
                            sort="new",
                            limit=20
                        )
                        
                        for post in search_results:
                            # 检查帖子日期
                            post_date = datetime.fromtimestamp(post.created_utc)
                            post_date_only = post_date.date()
                            target_date_only = target_date.date()
                            
                            if post_date_only != target_date_only:
                                continue
                            
                            # 检查相关性
                            if not self.is_relevant_post(post, keywords):
                                continue
                            
                            # 计算情绪得分
                            full_text = f"{post.title} {post.selftext}"
                            sentiment = self.calculate_sentiment_score(full_text)
                            
                            # 构建帖子数据
                            post_data = {
                                'id': post.id,
                                'title': post.title,
                                'content': post.selftext,
                                'url': post.url,
                                'permalink': f"https://reddit.com{post.permalink}",
                                'score': post.score,
                                'upvote_ratio': post.upvote_ratio,
                                'num_comments': post.num_comments,
                                'created_utc': post.created_utc,
                                'created_date': post_date.strftime("%Y-%m-%d %H:%M:%S"),
                                'author': str(post.author) if post.author else '[deleted]',
                                'subreddit': subreddit_name,
                                'search_keyword': keyword,
                                'sentiment': sentiment
                            }
                            
                            all_posts.append(post_data)
                            
                            if len(all_posts) >= self.min_posts_per_day * 2:  # 获取足够的数据
                                break
                        
                        # 添加延迟避免API限制
                        time.sleep(1)
                        
                    except Exception as e:
                        self.logger.warning(f"     [WARNING] 搜索 {keyword} 在 {subreddit_name} 失败: {e}")
                        continue
                
                # 添加subreddit间延迟
                time.sleep(2)
                
            except Exception as e:
                self.logger.warning(f"     [WARNING] subreddit {subreddit_name} 访问失败: {e}")
                continue
        
        # 去重（基于帖子ID）
        seen_ids = set()
        unique_posts = []
        for post in all_posts:
            if post['id'] not in seen_ids:
                seen_ids.add(post['id'])
                unique_posts.append(post)
        
        # 按分数排序
        unique_posts.sort(key=lambda x: x['score'], reverse=True)
        
        self.logger.info(f"     [SUCCESS] 找到 {len(unique_posts)} 个相关帖子")
        
        return unique_posts
    
    def save_daily_posts(self, ticker: str, date: datetime, posts: List[Dict]):
        """保存每日帖子数据"""
        config = self.target_stocks[ticker]
        folder_path = Path(config['folder'])
        
        # 创建日期文件名
        date_str = date.strftime("%Y-%m-%d")
        json_file = folder_path / f"{date_str}.json"
        txt_file = folder_path / f"{date_str}.txt"
        
        # 计算统计信息
        total_posts = len(posts)
        sentiment_stats = {
            'positive': len([p for p in posts if p['sentiment']['label'] == 'positive']),
            'negative': len([p for p in posts if p['sentiment']['label'] == 'negative']),
            'neutral': len([p for p in posts if p['sentiment']['label'] == 'neutral'])
        }
        
        avg_sentiment = sum(p['sentiment']['score'] for p in posts) / len(posts) if posts else 0
        total_score = sum(p['score'] for p in posts)
        avg_score = total_score / len(posts) if posts else 0
        
        # 构建保存数据
        save_data = {
            'ticker': ticker,
            'date': date_str,
            'total_posts': total_posts,
            'posts': posts,
            'statistics': {
                'sentiment_distribution': sentiment_stats,
                'average_sentiment_score': round(avg_sentiment, 3),
                'total_reddit_score': total_score,
                'average_reddit_score': round(avg_score, 1),
                'subreddit_distribution': {}
            },
            'metadata': {
                'fetch_time': datetime.now().isoformat(),
                'min_posts_target': self.min_posts_per_day,
                'target_met': total_posts >= self.min_posts_per_day
            }
        }
        
        # 计算subreddit分布
        subreddit_count = defaultdict(int)
        for post in posts:
            subreddit_count[post['subreddit']] += 1
        save_data['statistics']['subreddit_distribution'] = dict(subreddit_count)
        
        # 保存JSON文件
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(save_data, f, ensure_ascii=False, indent=2)
        
        # 保存可读文本文件
        with open(txt_file, 'w', encoding='utf-8') as f:
            f.write(f"{ticker} Reddit帖子数据 - {date_str}\n")
            f.write("=" * 50 + "\n\n")
            
            f.write(f"总帖子数: {total_posts}\n")
            target_status = "[SUCCESS]" if total_posts >= self.min_posts_per_day else "[INSUFFICIENT]"
            f.write(f"目标达成: {target_status}\n")
            f.write(f"平均情绪得分: {avg_sentiment:.3f}\n")
            f.write(f"平均Reddit得分: {avg_score:.1f}\n\n")
            
            f.write("情绪分布:\n")
            f.write(f"  正面: {sentiment_stats['positive']} 个\n")
            f.write(f"  负面: {sentiment_stats['negative']} 个\n")
            f.write(f"  中性: {sentiment_stats['neutral']} 个\n\n")
            
            f.write("帖子详情:\n")
            f.write("-" * 40 + "\n\n")
            
            for i, post in enumerate(posts, 1):
                f.write(f"{i}. [{post['subreddit']}] {post['title']}\n")
                f.write(f"   得分: {post['score']} | 评论: {post['num_comments']} | 情绪: {post['sentiment']['label']} ({post['sentiment']['score']})\n")
                f.write(f"   时间: {post['created_date']} | 作者: {post['author']}\n")
                
                if post['content']:
                    content_preview = post['content'][:150].replace('\n', ' ')
                    if len(post['content']) > 150:
                        content_preview += "..."
                    f.write(f"   内容: {content_preview}\n")
                
                f.write(f"   链接: {post['permalink']}\n\n")
        
        self.logger.info(f"     [SAVE] 保存到: {json_file}")
        
        return save_data

    def check_existing_data(self, ticker: str, date: datetime) -> bool:
        """检查是否已存在数据（支持断点续传）"""
        config = self.target_stocks[ticker]
        folder_path = Path(config['folder'])
        date_str = date.strftime("%Y-%m-%d")
        json_file = folder_path / f"{date_str}.json"

        if not json_file.exists():
            return False

        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # 检查是否达到最小帖子数要求
            total_posts = data.get('total_posts', 0)
            target_met = data.get('metadata', {}).get('target_met', False)

            if total_posts >= self.min_posts_per_day and target_met:
                self.logger.info(f"     [SKIP] 已存在足够数据 ({total_posts} 个帖子)")
                return True
            else:
                self.logger.info(f"     [RETRY] 数据不足，需要重新获取 (当前: {total_posts} 个)")
                return False

        except Exception as e:
            self.logger.warning(f"     [WARNING] 读取现有数据失败: {e}")
            return False

    def fetch_stock_data(self, ticker: str):
        """获取单个股票的所有数据"""
        self.logger.info(f"\n[START] 开始获取 {ticker} 数据...")

        dates = self.get_date_range()

        total_days = len(dates)
        completed_days = 0
        skipped_days = 0
        failed_days = 0

        for i, date in enumerate(dates, 1):
            self.logger.info(f"\n[PROCESS] 处理 {ticker} - {date.strftime('%Y-%m-%d')} ({i}/{total_days})")

            try:
                # 检查是否已存在数据
                if self.check_existing_data(ticker, date):
                    skipped_days += 1
                    continue

                # 获取帖子数据
                posts = self.fetch_posts_for_date(ticker, date)

                # 检查是否达到最小要求
                if len(posts) < self.min_posts_per_day:
                    self.logger.warning(f"     [WARNING] 帖子数量不足: {len(posts)}/{self.min_posts_per_day}")

                # 保存数据
                save_data = self.save_daily_posts(ticker, date, posts)

                if len(posts) >= self.min_posts_per_day:
                    completed_days += 1
                    self.logger.info(f"     [SUCCESS] 完成 ({len(posts)} 个帖子)")
                else:
                    failed_days += 1
                    self.logger.warning(f"     [INSUFFICIENT] 未达到目标 ({len(posts)} 个帖子)")

                # 添加延迟避免API限制
                time.sleep(3)

            except Exception as e:
                failed_days += 1
                self.logger.error(f"     [ERROR] 处理失败: {e}")
                time.sleep(5)  # 错误后等待更长时间
                continue

        # 输出统计信息
        self.logger.info(f"\n[SUMMARY] {ticker} 数据获取完成:")
        self.logger.info(f"   总天数: {total_days}")
        self.logger.info(f"   完成: {completed_days}")
        self.logger.info(f"   跳过: {skipped_days}")
        self.logger.info(f"   失败: {failed_days}")
        self.logger.info(f"   成功率: {(completed_days + skipped_days) / total_days * 100:.1f}%")

    def generate_summary_report(self):
        """生成总结报告"""
        self.logger.info("\n[REPORT] 生成总结报告...")

        summary_data = {
            'generation_time': datetime.now().isoformat(),
            'date_range': {
                'start': self.start_date.strftime('%Y-%m-%d'),
                'end': self.end_date.strftime('%Y-%m-%d'),
                'total_days': (self.end_date - self.start_date).days + 1
            },
            'stocks': {}
        }

        for ticker, config in self.target_stocks.items():
            folder_path = Path(config['folder'])

            if not folder_path.exists():
                continue

            stock_stats = {
                'total_files': 0,
                'total_posts': 0,
                'days_with_data': 0,
                'days_meeting_target': 0,
                'average_sentiment': 0,
                'sentiment_distribution': {'positive': 0, 'negative': 0, 'neutral': 0},
                'top_subreddits': {},
                'date_range_coverage': []
            }

            json_files = list(folder_path.glob("*.json"))
            stock_stats['total_files'] = len(json_files)

            all_sentiments = []
            subreddit_counts = defaultdict(int)

            for json_file in json_files:
                try:
                    with open(json_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)

                    posts_count = data.get('total_posts', 0)
                    stock_stats['total_posts'] += posts_count

                    if posts_count > 0:
                        stock_stats['days_with_data'] += 1

                    if data.get('metadata', {}).get('target_met', False):
                        stock_stats['days_meeting_target'] += 1

                    # 统计情绪
                    sentiment_dist = data.get('statistics', {}).get('sentiment_distribution', {})
                    for sentiment, count in sentiment_dist.items():
                        stock_stats['sentiment_distribution'][sentiment] += count

                    # 收集平均情绪得分
                    avg_sentiment = data.get('statistics', {}).get('average_sentiment_score', 0)
                    if avg_sentiment != 0:
                        all_sentiments.append(avg_sentiment)

                    # 统计subreddit
                    subreddit_dist = data.get('statistics', {}).get('subreddit_distribution', {})
                    for subreddit, count in subreddit_dist.items():
                        subreddit_counts[subreddit] += count

                    # 记录日期
                    stock_stats['date_range_coverage'].append(data.get('date'))

                except Exception as e:
                    self.logger.warning(f"读取文件 {json_file} 失败: {e}")
                    continue

            # 计算平均情绪
            if all_sentiments:
                stock_stats['average_sentiment'] = round(sum(all_sentiments) / len(all_sentiments), 3)

            # 排序subreddit
            stock_stats['top_subreddits'] = dict(sorted(subreddit_counts.items(), key=lambda x: x[1], reverse=True)[:5])

            # 排序日期覆盖
            stock_stats['date_range_coverage'].sort()

            summary_data['stocks'][ticker] = stock_stats

        # 保存总结报告
        summary_file = Path("reddit_batch_summary.json")
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary_data, f, ensure_ascii=False, indent=2)

        # 生成可读报告
        report_file = Path("reddit_batch_report.txt")
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("Reddit股票数据批量获取总结报告\n")
            f.write("=" * 50 + "\n\n")

            f.write(f"生成时间: {summary_data['generation_time']}\n")
            f.write(f"数据时间范围: {summary_data['date_range']['start']} 到 {summary_data['date_range']['end']}\n")
            f.write(f"总天数: {summary_data['date_range']['total_days']}\n\n")

            for ticker, stats in summary_data['stocks'].items():
                f.write(f"{ticker} 统计信息:\n")
                f.write("-" * 30 + "\n")
                f.write(f"  数据文件数: {stats['total_files']}\n")
                f.write(f"  总帖子数: {stats['total_posts']}\n")
                f.write(f"  有数据天数: {stats['days_with_data']}\n")
                f.write(f"  达标天数: {stats['days_meeting_target']}\n")
                f.write(f"  达标率: {stats['days_meeting_target'] / summary_data['date_range']['total_days'] * 100:.1f}%\n")
                f.write(f"  平均情绪得分: {stats['average_sentiment']}\n")

                f.write(f"  情绪分布:\n")
                for sentiment, count in stats['sentiment_distribution'].items():
                    f.write(f"    {sentiment}: {count}\n")

                f.write(f"  主要subreddit:\n")
                for subreddit, count in stats['top_subreddits'].items():
                    f.write(f"    {subreddit}: {count}\n")

                f.write("\n")

        self.logger.info(f"[REPORT] 总结报告已保存: {summary_file}")
        self.logger.info(f"[REPORT] 可读报告已保存: {report_file}")

    def run_batch_fetch(self):
        """运行批量获取"""
        self.logger.info("[START] 开始Reddit股票数据批量获取")
        self.logger.info(f"[CONFIG] 时间范围: {self.start_date.strftime('%Y-%m-%d')} 到 {self.end_date.strftime('%Y-%m-%d')}")
        self.logger.info(f"[CONFIG] 目标股票: {list(self.target_stocks.keys())}")
        self.logger.info(f"[CONFIG] 每日最小帖子数: {self.min_posts_per_day}")

        start_time = time.time()

        # 创建文件夹结构
        self.create_folder_structure()

        # 逐个处理股票
        for ticker in self.target_stocks.keys():
            try:
                self.fetch_stock_data(ticker)
            except Exception as e:
                self.logger.error(f"[ERROR] {ticker} 处理失败: {e}")
                continue

        # 生成总结报告
        self.generate_summary_report()

        end_time = time.time()
        total_time = end_time - start_time

        self.logger.info(f"\n[COMPLETE] 批量获取完成!")
        self.logger.info(f"[TIME] 总耗时: {total_time / 3600:.2f} 小时")
        self.logger.info(f"[OUTPUT] 数据保存在各股票文件夹中")
        self.logger.info(f"[REPORT] 查看总结报告: reddit_batch_report.txt")


def main():
    """主函数"""
    print("[START] Reddit股票数据批量获取脚本 - 编码修复版")
    print("=" * 50)
    print("目标股票: AAPL, MSFT, NVDA")
    print("时间范围: 2025-01-01 到 2025-06-25")
    print("每日目标: 至少10条帖子")
    print("包含功能: 情绪分析、按日期存储")
    print("=" * 50)

    try:
        fetcher = RedditBatchFetcherFixed()
        fetcher.run_batch_fetch()

    except KeyboardInterrupt:
        print("\n[INTERRUPT] 用户中断执行")
    except Exception as e:
        print(f"\n[ERROR] 执行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
