# 🚀 TradingAgents Quick Start with DeepSeek

Get up and running with TradingAgents using DeepSeek models in just a few minutes!

## 📋 Prerequisites

- Python 3.10 or higher
- DeepSeek API key (recommended) or OpenAI API key
- FinnHub API key (free)

## ⚡ Quick Setup

### 1. <PERSON>lone and Install

```bash
git clone https://github.com/TauricResearch/TradingAgents.git
cd TradingAgents
```

### 2. Automated Setup (Recommended)

```bash
python setup_deepseek.py
```

This script will:
- Install all dependencies
- Create a `.env` file
- Prompt for your API keys
- Test the setup

### 3. Manual Setup (Alternative)

```bash
# Install dependencies
pip install -r requirements.txt

# Create .env file
cp .env.example .env

# Edit .env file with your API keys
nano .env  # or use your preferred editor
```

Add your API keys to `.env`:
```env
DEEPSEEK_API_KEY=your_deepseek_api_key_here
FINNHUB_API_KEY=your_finnhub_api_key_here
```

## 🔑 Getting API Keys

### DeepSeek API (Primary - Recommended)
1. Visit [DeepSeek Platform](https://platform.deepseek.com/api_keys)
2. Sign up for an account
3. Create an API key
4. Copy the key to your `.env` file

### FinnHub API (Required)
1. Visit [FinnHub](https://finnhub.io/register)
2. Sign up for a free account
3. Get your API key from the dashboard
4. Copy the key to your `.env` file

## 🧪 Test Your Setup

```bash
python test_deepseek.py
```

This will verify:
- ✅ Environment variables are set
- ✅ DeepSeek API is working
- ✅ TradingAgents can initialize
- ✅ Basic functionality works

## 🎯 Run Your First Analysis

### Option 1: Interactive CLI (Recommended for beginners)

```bash
python -m cli.main
```

Follow the prompts to:
1. Select a stock ticker (e.g., AAPL, TSLA, NVDA)
2. Choose analysis date
3. Pick analysts (start with "Market Analyst")
4. Set research depth (start with "Light")
5. Select models (DeepSeek models are pre-selected)

### Option 2: Python Script

```bash
python main.py
```

This runs a basic analysis of NVDA using DeepSeek models.

### Option 3: Custom Analysis

```python
from tradingagents.graph.trading_graph import TradingAgentsGraph
from tradingagents.default_config import DEFAULT_CONFIG

# Configure for DeepSeek
config = DEFAULT_CONFIG.copy()
config["llm_provider"] = "deepseek"
config["deep_think_llm"] = "deepseek-reasoner"
config["quick_think_llm"] = "deepseek-chat"

# Initialize TradingAgents
ta = TradingAgentsGraph(debug=True, config=config)

# Run analysis
_, decision = ta.propagate("AAPL", "2024-05-10")
print(f"Trading Decision: {decision}")
```

## 📊 Example Outputs

After running an analysis, you'll see:

```
🤖 Market Analyst: Analyzing technical indicators...
📰 News Analyst: Processing recent news...
🐂 Bull Researcher: Arguing for positive outlook...
🐻 Bear Researcher: Highlighting risks...
⚖️ Research Manager: Synthesizing debate...
💼 Trader: Formulating trading plan...
🛡️ Risk Manager: Assessing risk factors...
📋 Portfolio Manager: Making final decision...

🎯 Final Decision: BUY - Strong technical momentum with positive news sentiment
```

## 🎛️ Configuration Options

### Cost-Optimized Setup
```python
config = DEFAULT_CONFIG.copy()
config["llm_provider"] = "deepseek"
config["deep_think_llm"] = "deepseek-chat"      # Use same model
config["quick_think_llm"] = "deepseek-chat"     # for both roles
config["max_debate_rounds"] = 1                 # Minimal debate
config["online_tools"] = False                  # Use cached data
```

### Comprehensive Analysis
```python
config = DEFAULT_CONFIG.copy()
config["llm_provider"] = "deepseek"
config["deep_think_llm"] = "deepseek-reasoner"  # Advanced reasoning
config["quick_think_llm"] = "deepseek-chat"     # Fast analysis
config["max_debate_rounds"] = 3                 # Thorough debate
config["online_tools"] = True                   # Real-time data
```

## 🔧 Troubleshooting

### Common Issues

**"DEEPSEEK_API_KEY environment variable is required"**
- Make sure your `.env` file exists and contains your API key
- Check that the key is correctly formatted (no extra spaces)

**"Rate limit exceeded"**
- DeepSeek has generous rate limits, but if you hit them, wait a moment and retry
- Consider using `online_tools: False` to reduce API calls

**"Module not found" errors**
- Run `pip install -r requirements.txt` again
- Make sure you're in the TradingAgents directory

### Getting Help

- 📖 Read the full [README.md](README.md) for detailed documentation
- 🧪 Run `python test_deepseek.py` to diagnose issues
- 💬 Join our [Discord community](https://discord.com/invite/hk9PGKShPK)
- 🐛 Report issues on [GitHub](https://github.com/TauricResearch/TradingAgents/issues)

## 🎉 Next Steps

1. **Explore Examples**: Check out `examples/deepseek_example.py` for more usage patterns
2. **Customize Analysts**: Modify which analysts to include in your analysis
3. **Backtest Strategies**: Use historical dates to test trading strategies
4. **Learn from Memory**: Let agents learn from past decisions with `ta.reflect_and_remember()`

## 💡 Pro Tips

- **Start Simple**: Begin with single analyst (Market) and light depth
- **Cost Control**: DeepSeek is very cost-effective, but monitor usage for large-scale analysis
- **Real-time vs Cached**: Use `online_tools: True` for latest data, `False` for speed
- **Model Selection**: `deepseek-reasoner` for complex analysis, `deepseek-chat` for speed

Happy Trading! 🚀📈
