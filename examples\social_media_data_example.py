#!/usr/bin/env python3
"""
社交媒体数据获取示例

这个示例展示了如何使用社交媒体数据获取器来获取和分析股票相关的社交媒体数据。
"""

import sys
from pathlib import Path
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from scripts.social_media_data_fetcher import SocialMediaDataFetcher


def example_online_fetch():
    """在线获取示例"""
    print("🌐 在线获取示例")
    print("=" * 40)
    
    # 初始化获取器
    fetcher = SocialMediaDataFetcher(debug=True)
    
    # 获取AAPL的社交媒体数据
    ticker = "AAPL"
    date = "2024-05-20"
    
    print(f"获取 {ticker} 在 {date} 的在线社交媒体数据...")
    
    result = fetcher.fetch_online_data(ticker, date)
    
    # 显示结果
    fetcher.display_results(result)
    
    # 保存结果
    if result.get('success'):
        filepath = fetcher.save_results(result)
        print(f"结果已保存到: {filepath}")
    
    return result


def example_offline_fetch():
    """离线获取示例"""
    print("\n💾 离线获取示例")
    print("=" * 40)
    
    # 初始化获取器
    fetcher = SocialMediaDataFetcher(debug=True)
    
    # 获取TSLA的Reddit数据
    ticker = "TSLA"
    date = "2024-05-20"
    
    print(f"获取 {ticker} 在 {date} 的离线Reddit数据...")
    
    result = fetcher.fetch_offline_data(ticker, date)
    
    # 显示结果
    fetcher.display_results(result)
    
    # 保存结果
    if result.get('success'):
        filepath = fetcher.save_results(result)
        print(f"结果已保存到: {filepath}")
    
    return result


def example_batch_fetch():
    """批量获取示例"""
    print("\n📦 批量获取示例")
    print("=" * 40)
    
    # 初始化获取器
    fetcher = SocialMediaDataFetcher(debug=False)  # 减少输出
    
    # 要分析的股票列表
    tickers = ["AAPL", "TSLA", "NVDA", "MSFT"]
    date = "2024-05-20"
    
    results = []
    
    for ticker in tickers:
        print(f"\n处理 {ticker}...")
        
        # 尝试在线获取
        online_result = fetcher.fetch_online_data(ticker, date)
        results.append(online_result)
        
        # 尝试离线获取
        offline_result = fetcher.fetch_offline_data(ticker, date)
        results.append(offline_result)
        
        # 简要显示结果
        online_status = "✅" if online_result.get('success') else "❌"
        offline_status = "✅" if offline_result.get('success') else "❌"
        
        online_length = online_result.get('content_length', 0)
        offline_length = offline_result.get('content_length', 0)
        
        print(f"  在线: {online_status} ({online_length} 字符)")
        print(f"  离线: {offline_status} ({offline_length} 字符)")
    
    # 统计结果
    successful = sum(1 for r in results if r.get('success'))
    print(f"\n📊 批量获取完成: {successful}/{len(results)} 成功")
    
    return results


def example_quality_analysis():
    """内容质量分析示例"""
    print("\n⭐ 内容质量分析示例")
    print("=" * 40)
    
    fetcher = SocialMediaDataFetcher(debug=False)
    
    # 测试不同质量的内容
    test_contents = [
        "",  # 空内容
        "简短内容",  # 太短
        "这是一个关于股票投资的讨论，包含了很多有用的分析和观点。投资者对这只股票的看法比较乐观，认为未来有上涨潜力。" * 10,  # 中等质量
        """
        ### AAPL 股票分析讨论
        
        **投资者观点:**
        - 看涨: 60%的投资者认为苹果股票会继续上涨
        - 看跌: 25%的投资者担心市场调整
        - 中性: 15%的投资者持观望态度
        
        **技术分析:**
        - RSI指标显示超买状态
        - 移动平均线呈现上升趋势
        - 成交量有所放大
        
        **社区讨论热点:**
        1. iPhone新产品发布预期
        2. 服务业务增长前景
        3. 中国市场表现担忧
        
        **情绪分析:**
        整体情绪偏向乐观，但存在一定分歧。
        """,  # 高质量内容
        "I cannot search for social media content as I don't have access to real-time data."  # 指导性文本
    ]
    
    for i, content in enumerate(test_contents, 1):
        score = fetcher._assess_content_quality(content)
        print(f"内容 {i}: 质量评分 {score:.1f}/10")
        print(f"  长度: {len(content)} 字符")
        print(f"  预览: {content[:50]}{'...' if len(content) > 50 else ''}")
        print()


def main():
    """主函数"""
    print("🚀 社交媒体数据获取示例")
    print("=" * 50)
    
    try:
        # 1. 在线获取示例
        example_online_fetch()
        
        # 2. 离线获取示例  
        example_offline_fetch()
        
        # 3. 批量获取示例
        example_batch_fetch()
        
        # 4. 质量分析示例
        example_quality_analysis()
        
        print("\n🎯 所有示例执行完成!")
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断执行")
    except Exception as e:
        print(f"\n❌ 执行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
