# 模型对比配置 - YAML格式示例
experiment_name: "model_comparison"
description: "模型参数对比测试 - 不同温度设置的影响"

# 时间范围
start_date: "2024-01-01"
end_date: "2024-03-31"

# 股票列表 - 选择代表性股票
symbols:
  - "AAPL"  # 科技股
  - "MSFT"  # 科技股
  - "JPM"   # 金融股
  - "JNJ"   # 医疗股

# 模型配置
model_config:
  provider: "deepseek"
  deep_think_model: "deepseek-reasoner"
  quick_think_model: "deepseek-chat"
  temperature: 0.3  # 中等温度设置
  max_tokens: null

# 代理配置
agent_config:
  selected_analysts:
    - "market"
    - "news"
    - "fundamentals"
  max_debate_rounds: 1
  max_risk_discuss_rounds: 1
  enable_memory: true

# 数据配置
online_tools: false
data_cache_enabled: true

# 输出配置
output_dir: "backtest_results/model_comparison"
save_detailed_logs: true
save_trade_decisions: true

# 基准和风险配置
benchmark_symbol: "SPY"
risk_free_rate: 0.02
