"""
回测执行引擎

核心回测执行逻辑，包括数据验证、批量处理、错误处理和进度跟踪。
"""

import os
import json
import traceback
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
import pandas as pd
from tqdm import tqdm
import logging

from ..graph.trading_graph import TradingAgentsGraph
from ..default_config import DEFAULT_CONFIG
from .config import BacktestConfig
from .experiment import ExperimentManager


class BacktestEngine:
    """回测执行引擎"""
    
    def __init__(self, experiment_manager: ExperimentManager = None):
        """
        初始化回测引擎
        
        Args:
            experiment_manager: 实验管理器
        """
        self.experiment_manager = experiment_manager or ExperimentManager()
        self.logger = self._setup_logger()
        
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger("BacktestEngine")
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def validate_data_availability(self, config: BacktestConfig) -> Dict[str, List[str]]:
        """
        验证数据可用性
        
        Args:
            config: 回测配置
            
        Returns:
            数据可用性报告
        """
        self.logger.info("🔍 验证数据可用性...")
        
        report = {
            "available_symbols": [],
            "missing_symbols": [],
            "date_range_issues": []
        }
        
        # 检查数据目录
        data_dir = Path(DEFAULT_CONFIG.get("data_dir", "data"))
        if not data_dir.exists():
            self.logger.warning(f"数据目录不存在: {data_dir}")
            report["missing_symbols"] = config.symbols.copy()
            return report
        
        # 检查每个股票的数据
        for symbol in config.symbols:
            data_file = data_dir / "market_data" / "price_data" / f"{symbol}-YFin-data-2015-01-01-2025-03-25.csv"
            
            if data_file.exists():
                try:
                    # 检查日期范围
                    df = pd.read_csv(data_file)
                    if 'Date' in df.columns:
                        df['Date'] = pd.to_datetime(df['Date'])
                        min_date = df['Date'].min().strftime('%Y-%m-%d')
                        max_date = df['Date'].max().strftime('%Y-%m-%d')
                        
                        if config.start_date < min_date or config.end_date > max_date:
                            report["date_range_issues"].append({
                                "symbol": symbol,
                                "requested_range": f"{config.start_date} ~ {config.end_date}",
                                "available_range": f"{min_date} ~ {max_date}"
                            })
                        else:
                            report["available_symbols"].append(symbol)
                    else:
                        report["missing_symbols"].append(symbol)
                        
                except Exception as e:
                    self.logger.warning(f"读取 {symbol} 数据时出错: {e}")
                    report["missing_symbols"].append(symbol)
            else:
                report["missing_symbols"].append(symbol)
        
        # 打印报告
        self.logger.info(f"✅ 可用股票: {len(report['available_symbols'])}")
        self.logger.info(f"❌ 缺失股票: {len(report['missing_symbols'])}")
        if report["date_range_issues"]:
            self.logger.warning(f"⚠️  日期范围问题: {len(report['date_range_issues'])}")
        
        return report
    
    def generate_trading_dates(self, start_date: str, end_date: str, 
                             frequency: str = "weekly") -> List[str]:
        """
        生成交易日期序列
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            frequency: 频率 ("daily", "weekly", "monthly")
            
        Returns:
            交易日期列表
        """
        start = datetime.strptime(start_date, "%Y-%m-%d")
        end = datetime.strptime(end_date, "%Y-%m-%d")
        
        dates = []
        current = start
        
        if frequency == "daily":
            delta = timedelta(days=1)
        elif frequency == "weekly":
            delta = timedelta(weeks=1)
        elif frequency == "monthly":
            delta = timedelta(days=30)
        else:
            raise ValueError(f"不支持的频率: {frequency}")
        
        while current <= end:
            # 跳过周末（假设周一到周五为交易日）
            if current.weekday() < 5:  # 0-4 为周一到周五
                dates.append(current.strftime("%Y-%m-%d"))
            current += delta
        
        return dates
    
    def run_single_analysis(self, symbol: str, date: str, 
                          trading_graph: TradingAgentsGraph) -> Dict[str, Any]:
        """
        运行单次分析
        
        Args:
            symbol: 股票代码
            date: 交易日期
            trading_graph: 交易图实例
            
        Returns:
            分析结果
        """
        try:
            self.logger.debug(f"分析 {symbol} 在 {date}")
            
            # 运行分析
            final_state, decision = trading_graph.propagate(symbol, date)
            
            # 提取关键信息
            result = {
                "symbol": symbol,
                "date": date,
                "decision": decision,
                "success": True,
                "timestamp": datetime.now().isoformat(),
                "final_state": final_state
            }
            
            # 提取报告摘要
            reports = {}
            report_keys = [
                "market_report", "sentiment_report", "news_report", 
                "fundamentals_report", "investment_plan", "trader_investment_plan"
            ]
            
            for key in report_keys:
                if key in final_state and final_state[key]:
                    content = str(final_state[key])
                    reports[key] = {
                        "summary": content[:500] + "..." if len(content) > 500 else content,
                        "length": len(content)
                    }
            
            result["reports"] = reports
            
            return result
            
        except Exception as e:
            self.logger.error(f"分析 {symbol} 在 {date} 时出错: {e}")
            return {
                "symbol": symbol,
                "date": date,
                "decision": "ERROR",
                "success": False,
                "error": str(e),
                "traceback": traceback.format_exc(),
                "timestamp": datetime.now().isoformat()
            }
    
    def run_backtest(self, config: BacktestConfig, experiment_id: str = None) -> Dict[str, Any]:
        """
        运行回测
        
        Args:
            config: 回测配置
            experiment_id: 实验ID（如果为None则创建新实验）
            
        Returns:
            回测结果
        """
        # 验证配置
        config.validate()
        
        # 创建或获取实验
        if experiment_id is None:
            experiment_id = self.experiment_manager.create_experiment(config)
        
        exp_dir = self.experiment_manager.get_experiment_directory(experiment_id)
        
        try:
            # 更新实验状态
            self.experiment_manager.update_experiment_status(experiment_id, "running")
            
            self.logger.info(f"🚀 开始回测实验: {experiment_id}")
            self.logger.info(f"   配置: {config.experiment_name}")
            self.logger.info(f"   时间范围: {config.start_date} ~ {config.end_date}")
            self.logger.info(f"   股票数量: {len(config.symbols)}")
            
            # 验证数据可用性
            data_report = self.validate_data_availability(config)
            if data_report["missing_symbols"]:
                self.logger.warning(f"缺失数据的股票: {data_report['missing_symbols']}")
            
            # 使用可用的股票
            available_symbols = data_report["available_symbols"]
            if not available_symbols:
                raise ValueError("没有可用的股票数据")
            
            # 创建TradingAgentsGraph配置
            ta_config = DEFAULT_CONFIG.copy()
            ta_config.update({
                "llm_provider": config.model_config.provider,
                "deep_think_llm": config.model_config.deep_think_model,
                "quick_think_llm": config.model_config.quick_think_model,
                "max_debate_rounds": config.agent_config.max_debate_rounds,
                "max_risk_discuss_rounds": config.agent_config.max_risk_discuss_rounds,
                "online_tools": config.online_tools
            })
            
            # 初始化TradingAgentsGraph
            trading_graph = TradingAgentsGraph(
                selected_analysts=config.agent_config.selected_analysts,
                debug=False,  # 批量处理时关闭debug模式
                config=ta_config
            )
            
            # 生成交易日期
            trading_dates = self.generate_trading_dates(
                config.start_date, 
                config.end_date,
                frequency="weekly"  # 默认每周交易
            )
            
            self.logger.info(f"📅 生成交易日期: {len(trading_dates)} 个")
            
            # 执行回测
            results = []
            total_analyses = len(available_symbols) * len(trading_dates)
            
            with tqdm(total=total_analyses, desc="回测进度") as pbar:
                for symbol in available_symbols:
                    for date in trading_dates:
                        result = self.run_single_analysis(symbol, date, trading_graph)
                        results.append(result)
                        
                        # 保存中间结果
                        if len(results) % 10 == 0:
                            self._save_intermediate_results(exp_dir, results)
                        
                        pbar.update(1)
                        pbar.set_postfix({
                            "当前": f"{symbol}@{date}",
                            "成功": sum(1 for r in results if r["success"]),
                            "失败": sum(1 for r in results if not r["success"])
                        })
            
            # 汇总结果
            summary = self._summarize_results(results, config)
            
            # 保存最终结果
            self._save_final_results(exp_dir, results, summary)
            
            # 更新实验状态
            self.experiment_manager.save_experiment_results(experiment_id, summary)
            
            self.logger.info(f"✅ 回测完成: {experiment_id}")
            self.logger.info(f"   总分析次数: {len(results)}")
            self.logger.info(f"   成功率: {summary['execution_summary']['success_rate']:.2%}")
            
            return {
                "experiment_id": experiment_id,
                "config": config,
                "results": results,
                "summary": summary,
                "data_report": data_report
            }
            
        except Exception as e:
            self.logger.error(f"回测失败: {e}")
            self.experiment_manager.update_experiment_status(
                experiment_id, 
                "failed",
                {"error": str(e), "traceback": traceback.format_exc()}
            )
            raise e
    
    def _save_intermediate_results(self, exp_dir: Path, results: List[Dict]) -> None:
        """保存中间结果"""
        results_file = exp_dir / "results" / "intermediate_results.json"
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False, default=str)
    
    def _save_final_results(self, exp_dir: Path, results: List[Dict], summary: Dict) -> None:
        """保存最终结果"""
        # 保存详细结果
        results_file = exp_dir / "results" / "detailed_results.json"
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False, default=str)
        
        # 保存摘要
        summary_file = exp_dir / "results" / "summary.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False, default=str)
        
        # 保存CSV格式的交易记录
        trades_df = pd.DataFrame([
            {
                "symbol": r["symbol"],
                "date": r["date"],
                "decision": r["decision"],
                "success": r["success"]
            }
            for r in results
        ])
        trades_file = exp_dir / "results" / "trades.csv"
        trades_df.to_csv(trades_file, index=False, encoding='utf-8')
    
    def _summarize_results(self, results: List[Dict], config: BacktestConfig) -> Dict[str, Any]:
        """汇总结果"""
        total_analyses = len(results)
        successful_analyses = sum(1 for r in results if r["success"])
        
        # 统计决策分布
        decisions = [r["decision"] for r in results if r["success"]]
        decision_counts = {}
        for decision in decisions:
            decision_counts[decision] = decision_counts.get(decision, 0) + 1
        
        # 按股票统计
        symbol_stats = {}
        for result in results:
            symbol = result["symbol"]
            if symbol not in symbol_stats:
                symbol_stats[symbol] = {"total": 0, "success": 0, "decisions": {}}
            
            symbol_stats[symbol]["total"] += 1
            if result["success"]:
                symbol_stats[symbol]["success"] += 1
                decision = result["decision"]
                symbol_stats[symbol]["decisions"][decision] = \
                    symbol_stats[symbol]["decisions"].get(decision, 0) + 1
        
        return {
            "experiment_info": {
                "name": config.experiment_name,
                "start_date": config.start_date,
                "end_date": config.end_date,
                "symbols": config.symbols,
                "model_provider": config.model_config.provider
            },
            "execution_summary": {
                "total_analyses": total_analyses,
                "successful_analyses": successful_analyses,
                "failed_analyses": total_analyses - successful_analyses,
                "success_rate": successful_analyses / total_analyses if total_analyses > 0 else 0
            },
            "decision_distribution": decision_counts,
            "symbol_statistics": symbol_stats,
            "generated_at": datetime.now().isoformat()
        }
