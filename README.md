<p align="center">
  <img src="assets/TauricResearch.png" style="width: 60%; height: auto;">
</p>

<div align="center" style="line-height: 1;">
  <a href="https://arxiv.org/abs/2412.20138" target="_blank"><img alt="arXiv" src="https://img.shields.io/badge/arXiv-2412.20138-B31B1B?logo=arxiv"/></a>
  <a href="https://discord.com/invite/hk9PGKShPK" target="_blank"><img alt="Discord" src="https://img.shields.io/badge/Discord-TradingResearch-7289da?logo=discord&logoColor=white&color=7289da"/></a>
  <a href="./assets/wechat.png" target="_blank"><img alt="WeChat" src="https://img.shields.io/badge/WeChat-TauricResearch-brightgreen?logo=wechat&logoColor=white"/></a>
  <a href="https://x.com/TauricResearch" target="_blank"><img alt="X Follow" src="https://img.shields.io/badge/X-TauricResearch-white?logo=x&logoColor=white"/></a>
  <br>
  <a href="https://github.com/TauricResearch/" target="_blank"><img alt="Community" src="https://img.shields.io/badge/Join_GitHub_Community-TauricResearch-14C290?logo=discourse"/></a>
</div>

<div align="center">
  <!-- Keep these links. Translations will automatically update with the README. -->
  <a href="https://www.readme-i18n.com/TauricResearch/TradingAgents?lang=de">Deutsch</a> | 
  <a href="https://www.readme-i18n.com/TauricResearch/TradingAgents?lang=es">Español</a> | 
  <a href="https://www.readme-i18n.com/TauricResearch/TradingAgents?lang=fr">français</a> | 
  <a href="https://www.readme-i18n.com/TauricResearch/TradingAgents?lang=ja">日本語</a> | 
  <a href="https://www.readme-i18n.com/TauricResearch/TradingAgents?lang=ko">한국어</a> | 
  <a href="https://www.readme-i18n.com/TauricResearch/TradingAgents?lang=pt">Português</a> | 
  <a href="https://www.readme-i18n.com/TauricResearch/TradingAgents?lang=ru">Русский</a> | 
  <a href="https://www.readme-i18n.com/TauricResearch/TradingAgents?lang=zh">中文</a>
</div>

---

# TradingAgents: Multi-Agents LLM Financial Trading Framework 

> 🎉 **TradingAgents** officially released! We have received numerous inquiries about the work, and we would like to express our thanks for the enthusiasm in our community.
>
> So we decided to fully open-source the framework. Looking forward to building impactful projects with you!

<div align="center">
<a href="https://www.star-history.com/#TauricResearch/TradingAgents&Date">
 <picture>
   <source media="(prefers-color-scheme: dark)" srcset="https://api.star-history.com/svg?repos=TauricResearch/TradingAgents&type=Date&theme=dark" />
   <source media="(prefers-color-scheme: light)" srcset="https://api.star-history.com/svg?repos=TauricResearch/TradingAgents&type=Date" />
   <img alt="TradingAgents Star History" src="https://api.star-history.com/svg?repos=TauricResearch/TradingAgents&type=Date" style="width: 80%; height: auto;" />
 </picture>
</a>
</div>

<div align="center">

🚀 [TradingAgents](#tradingagents-framework) | ⚡ [Installation & CLI](#installation-and-cli) | 🎬 [Demo](https://www.youtube.com/watch?v=90gr5lwjIho) | 📦 [Package Usage](#tradingagents-package) | 🤝 [Contributing](#contributing) | 📄 [Citation](#citation)

</div>

## TradingAgents Framework

TradingAgents is a multi-agent trading framework that mirrors the dynamics of real-world trading firms. By deploying specialized LLM-powered agents: from fundamental analysts, sentiment experts, and technical analysts, to trader, risk management team, the platform collaboratively evaluates market conditions and informs trading decisions. Moreover, these agents engage in dynamic discussions to pinpoint the optimal strategy.

<p align="center">
  <img src="assets/schema.png" style="width: 100%; height: auto;">
</p>

> TradingAgents framework is designed for research purposes. Trading performance may vary based on many factors, including the chosen backbone language models, model temperature, trading periods, the quality of data, and other non-deterministic factors. [It is not intended as financial, investment, or trading advice.](https://tauric.ai/disclaimer/)

Our framework decomposes complex trading tasks into specialized roles. This ensures the system achieves a robust, scalable approach to market analysis and decision-making.

### 🔄 Trading Workflow Overview

The TradingAgents framework follows a structured multi-stage workflow that simulates real-world trading firm operations:

1. **Data Collection Phase**: Multiple data sources are accessed simultaneously
   - Market data via Yahoo Finance and FinnHub APIs
   - News sentiment from Google News and Reddit
   - Company fundamentals and insider trading information
   - Technical indicators and market statistics

2. **Analysis Phase**: Specialized analyst agents process different data types
   - **Market Analyst**: Technical analysis using indicators (MACD, RSI, etc.)
   - **News Analyst**: Global news and macroeconomic impact analysis
   - **Social Media Analyst**: Sentiment analysis from Reddit and social platforms
   - **Fundamentals Analyst**: Company financials, insider sentiment, and SEC filings

3. **Research & Debate Phase**: Bull vs Bear researchers engage in structured debates
   - **Bull Researcher**: Argues for positive investment opportunities
   - **Bear Researcher**: Highlights risks and potential downsides
   - **Research Manager**: Synthesizes debates and creates investment recommendations

4. **Trading Decision Phase**: Trader agent formulates specific trading plans
   - Considers all analyst reports and research debates
   - Incorporates historical memory and past trading lessons
   - Proposes specific BUY/SELL/HOLD decisions with rationale

5. **Risk Assessment Phase**: Three-perspective risk analysis
   - **Risky Analyst**: Champions high-reward opportunities
   - **Conservative Analyst**: Emphasizes risk mitigation
   - **Neutral Analyst**: Provides balanced perspective
   - **Risk Manager**: Makes final risk-adjusted decisions

6. **Portfolio Management**: Final approval and execution
   - Reviews all previous analyses and risk assessments
   - Makes final BUY/SELL/HOLD decision
   - Simulates trade execution

### Analyst Team
- Fundamentals Analyst: Evaluates company financials and performance metrics, identifying intrinsic values and potential red flags.
- Sentiment Analyst: Analyzes social media and public sentiment using sentiment scoring algorithms to gauge short-term market mood.
- News Analyst: Monitors global news and macroeconomic indicators, interpreting the impact of events on market conditions.
- Technical Analyst: Utilizes technical indicators (like MACD and RSI) to detect trading patterns and forecast price movements.

<p align="center">
  <img src="assets/analyst.png" width="100%" style="display: inline-block; margin: 0 2%;">
</p>

### Researcher Team
- Comprises both bullish and bearish researchers who critically assess the insights provided by the Analyst Team. Through structured debates, they balance potential gains against inherent risks.

<p align="center">
  <img src="assets/researcher.png" width="70%" style="display: inline-block; margin: 0 2%;">
</p>

### Trader Agent
- Composes reports from the analysts and researchers to make informed trading decisions. It determines the timing and magnitude of trades based on comprehensive market insights.

<p align="center">
  <img src="assets/risk.png" width="70%" style="display: inline-block; margin: 0 2%;">
</p>

### Risk Management and Portfolio Manager
- Continuously evaluates portfolio risk by assessing market volatility, liquidity, and other risk factors. The risk management team evaluates and adjusts trading strategies, providing assessment reports to the Portfolio Manager for final decision.
- The Portfolio Manager approves/rejects the transaction proposal. If approved, the order will be sent to the simulated exchange and executed.

<p align="center">
  <img src="assets/trader.png" width="70%" style="display: inline-block; margin: 0 2%;">
</p>

## Installation and CLI

### Installation

Clone TradingAgents:
```bash
git clone https://github.com/TauricResearch/TradingAgents.git
cd TradingAgents
```

Create a virtual environment in any of your favorite environment managers:
```bash
conda create -n tradingagents python=3.13
conda activate tradingagents
```

Install dependencies:
```bash
pip install -r requirements.txt
```

### Required APIs

TradingAgents requires several API keys to access financial data and LLM services. All APIs can be used with their free tiers for testing purposes.

#### 🔑 Essential APIs (Required)

**1. DeepSeek API** - Powers all LLM agents (Recommended)
```bash
export DEEPSEEK_API_KEY=your_deepseek_api_key_here
```
- Get your API key from: https://platform.deepseek.com/api_keys
- Used for: All agent reasoning, analysis, and decision-making
- Models available: `deepseek-chat`, `deepseek-coder`, `deepseek-reasoner`
- Cost: Very cost-effective compared to OpenAI models

**Alternative: OpenAI API** - Fallback LLM provider
```bash
export OPENAI_API_KEY=your_openai_api_key_here
```
- Get your API key from: https://platform.openai.com/api-keys
- Used for: All agent reasoning, analysis, and decision-making
- Models used: `gpt-4o-mini`, `gpt-4.1-nano`, `o4-mini` (configurable)
- Cost: Framework makes many API calls, recommend using cheaper models for testing

**2. FinnHub API** - Financial data and company information
```bash
export FINNHUB_API_KEY=your_finnhub_api_key_here
```
- Get your free API key from: https://finnhub.io/register
- Used for: Company news, insider trading data, financial statements
- Free tier: 60 API calls/minute, sufficient for testing
- Data includes: Stock news, insider sentiment, SEC filings

#### 🔧 Optional APIs (For Enhanced Features)

**3. Reddit API (PRAW)** - Social sentiment analysis
```bash
export REDDIT_CLIENT_ID=your_reddit_client_id
export REDDIT_CLIENT_SECRET=your_reddit_client_secret
export REDDIT_USER_AGENT=your_app_name
```
- Get credentials from: https://www.reddit.com/prefs/apps
- Used for: Social media sentiment analysis from financial subreddits
- Required for: Social Media Analyst functionality

**4. Yahoo Finance** - Market data (No API key required)
- Automatically used via `yfinance` library
- Used for: Historical price data, technical indicators
- Free and unlimited for basic usage

#### 🌐 Environment Variables Setup

**For Windows (PowerShell):**
```powershell
$env:DEEPSEEK_API_KEY="your_deepseek_api_key_here"
$env:FINNHUB_API_KEY="your_finnhub_api_key_here"
$env:REDDIT_CLIENT_ID="your_reddit_client_id"
$env:REDDIT_CLIENT_SECRET="your_reddit_client_secret"
$env:REDDIT_USER_AGENT="TradingAgents/1.0"
# Optional: OpenAI as fallback
$env:OPENAI_API_KEY="your_openai_api_key_here"
```

**For Linux/macOS (Bash):**
```bash
export DEEPSEEK_API_KEY="your_deepseek_api_key_here"
export FINNHUB_API_KEY="your_finnhub_api_key_here"
export REDDIT_CLIENT_ID="your_reddit_client_id"
export REDDIT_CLIENT_SECRET="your_reddit_client_secret"
export REDDIT_USER_AGENT="TradingAgents/1.0"
# Optional: OpenAI as fallback
export OPENAI_API_KEY="your_openai_api_key_here"
```

**Using .env file (Recommended):**
Create a `.env` file in the project root:
```env
DEEPSEEK_API_KEY=your_deepseek_api_key_here
FINNHUB_API_KEY=your_finnhub_api_key_here
REDDIT_CLIENT_ID=your_reddit_client_id
REDDIT_CLIENT_SECRET=your_reddit_client_secret
REDDIT_USER_AGENT=TradingAgents/1.0
# Optional: OpenAI as fallback
OPENAI_API_KEY=your_openai_api_key_here
```

#### 💡 API Configuration Tips

- **Cost Management**: Use DeepSeek models for significant cost savings compared to OpenAI
- **Provider Priority**: Framework automatically detects DeepSeek API first, then falls back to OpenAI
- **Rate Limits**: FinnHub free tier has 60 calls/minute - framework handles this automatically
- **Data Sources**: Set `online_tools: True` in config for real-time data, `False` for cached data
- **Minimum Setup**: Framework can work with just DeepSeek + FinnHub APIs for basic functionality

### CLI Usage

The TradingAgents CLI provides an interactive interface for running trading analysis with customizable parameters.

#### 🚀 Quick Start
```bash
python -m cli.main
```

#### 📋 CLI Configuration Options

The CLI will guide you through 5 configuration steps:

**Step 1: Ticker Symbol**
- Enter any valid stock ticker (e.g., AAPL, TSLA, NVDA, SPY)
- Default: SPY (S&P 500 ETF)

**Step 2: Analysis Date**
- Format: YYYY-MM-DD
- Default: Current date
- Note: Historical data analysis works best with past dates

**Step 3: Analyst Team Selection**
- **Market Analyst**: Technical indicators and price analysis
- **Social Media Analyst**: Reddit sentiment and social trends
- **News Analyst**: Global news and macroeconomic events
- **Fundamentals Analyst**: Company financials and insider data
- You can select any combination of analysts

**Step 4: Research Depth**
- **Light (1 round)**: Quick analysis with minimal debate
- **Medium (2-3 rounds)**: Balanced analysis with moderate debate
- **Deep (4+ rounds)**: Comprehensive analysis with extensive debate
- More rounds = more thorough analysis but higher API costs

**Step 5: LLM Selection**
- **Quick Thinking Agent**: For fast analysis tasks
  - `gpt-4o-mini` (recommended for cost efficiency)
  - `gpt-4.1-nano` (ultra-lightweight)
  - `gpt-4.1-mini` (compact with good performance)
  - `gpt-4o` (standard model)
- **Deep Thinking Agent**: For complex reasoning tasks
  - `o4-mini` (recommended for cost efficiency)
  - `o1-preview` (advanced reasoning, higher cost)
  - `gpt-4o` (balanced performance)

#### 🖥️ CLI Interface Features

<p align="center">
  <img src="assets/cli/cli_init.png" width="100%" style="display: inline-block; margin: 0 2%;">
</p>

**Real-time Progress Tracking:**
- Live agent status updates
- Current analysis phase display
- Tool call monitoring
- Report generation progress

<p align="center">
  <img src="assets/cli/cli_news.png" width="100%" style="display: inline-block; margin: 0 2%;">
</p>

**Interactive Analysis Display:**
- Analyst team progress indicators
- Research debate summaries
- Risk assessment results
- Final trading recommendations

<p align="center">
  <img src="assets/cli/cli_transaction.png" width="100%" style="display: inline-block; margin: 0 2%;">
</p>

#### ⚡ CLI Tips

- **First Run**: Start with default settings (SPY, current date, all analysts, light depth)
- **Cost Control**: Use `gpt-4o-mini` + `o4-mini` for budget-friendly analysis
- **Comprehensive Analysis**: Select all analysts with deep research for thorough insights
- **Quick Testing**: Use single analyst (e.g., just Market Analyst) for rapid testing

## TradingAgents Package

### Implementation Details

TradingAgents is built with **LangGraph** to ensure flexibility and modularity in agent orchestration. The framework uses a state-based workflow where each agent processes and updates a shared state containing market data, analysis reports, and trading decisions.

**Key Technical Features:**
- **Multi-Agent Architecture**: Specialized agents for different analysis types
- **State Management**: Shared state across all agents with structured data flow
- **Memory System**: Agents learn from past trading decisions and outcomes
- **Debate Mechanisms**: Structured discussions between bull/bear researchers and risk analysts
- **Tool Integration**: Seamless API integration for real-time and historical data
- **Configurable LLMs**: Support for multiple OpenAI models with cost optimization

**Recommended Model Configuration:**
- **Production**: `o1-preview` (deep thinking) + `gpt-4o` (quick thinking)
- **Testing**: `o4-mini` (deep thinking) + `gpt-4o-mini` (quick thinking)
- **Budget**: `gpt-4.1-nano` for both (minimal cost, basic functionality)

### Python Usage

#### 🔧 Basic Usage

```python
from tradingagents.graph.trading_graph import TradingAgentsGraph
from tradingagents.default_config import DEFAULT_CONFIG

# Initialize with default configuration
ta = TradingAgentsGraph(debug=True, config=DEFAULT_CONFIG.copy())

# Run analysis for a specific stock and date
final_state, decision = ta.propagate("NVDA", "2024-05-10")
print(f"Trading Decision: {decision}")
```

#### ⚙️ Advanced Configuration

```python
from tradingagents.graph.trading_graph import TradingAgentsGraph
from tradingagents.default_config import DEFAULT_CONFIG

# Create custom configuration
config = DEFAULT_CONFIG.copy()
config.update({
    # LLM Configuration
    "deep_think_llm": "o4-mini",        # For complex reasoning
    "quick_think_llm": "gpt-4o-mini",   # For fast analysis

    # Debate Configuration
    "max_debate_rounds": 2,             # Bull vs Bear debate rounds
    "max_risk_discuss_rounds": 2,       # Risk analysis rounds

    # Data Configuration
    "online_tools": True,               # Use real-time data
    "max_recur_limit": 100,            # Maximum recursion depth
})

# Initialize with custom config
ta = TradingAgentsGraph(
    selected_analysts=["market", "news", "fundamentals"],  # Choose specific analysts
    debug=True,
    config=config
)

# Run analysis
final_state, decision = ta.propagate("AAPL", "2024-06-15")
print(f"Final Decision: {decision}")
```

#### 🧠 Memory and Learning

```python
# After getting trading results, update agent memory
returns_losses = 1500  # Profit/loss from the trade
ta.reflect_and_remember(returns_losses)

# Agents will use this experience in future decisions
```

#### 📊 Configuration Options

**Available Analysts:**
- `"market"`: Technical analysis and price indicators
- `"social"`: Social media sentiment analysis
- `"news"`: News and macroeconomic analysis
- `"fundamentals"`: Company financials and insider data

**LLM Models:**
- `"gpt-4o-mini"`: Fast, cost-effective (recommended for testing)
- `"gpt-4.1-nano"`: Ultra-lightweight, minimal cost
- `"gpt-4.1-mini"`: Compact with good performance
- `"gpt-4o"`: Standard model with solid capabilities
- `"o4-mini"`: Advanced reasoning, cost-effective
- `"o1-preview"`: Most advanced reasoning (higher cost)

**Data Sources:**
- `online_tools: True`: Real-time data from APIs
- `online_tools: False`: Cached historical data from Tauric TradingDB

> **Note**: For `online_tools`, we recommend enabling them for experimentation as they provide access to real-time data. The offline tools rely on cached data from our **Tauric TradingDB**, a curated dataset used for backtesting. We're refining this dataset and plan to release it soon.

#### 🔍 Debug and Monitoring

```python
# Enable debug mode for detailed logging
ta = TradingAgentsGraph(debug=True, config=config)

# Access internal state and logs
print(f"Current ticker: {ta.ticker}")
print(f"State history: {ta.log_states_dict}")

# Process signals for decision extraction
core_decision = ta.process_signal(final_state["final_trade_decision"])
```

View the complete configuration options in `tradingagents/default_config.py`.

## 🏗️ Architecture & Data Flow

### System Architecture

TradingAgents follows a modular, multi-agent architecture built on LangGraph:

```
┌─────────────────────────────────────────────────────────────┐
│                    TradingAgentsGraph                       │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   Analyst   │  │  Research   │  │    Risk     │         │
│  │    Team     │→ │    Team     │→ │ Management  │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
│         │                │                │                │
│         ▼                ▼                ▼                │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │    Data     │  │   Trader    │  │ Portfolio   │         │
│  │ Collection  │  │   Agent     │  │  Manager    │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
```

### Data Sources & APIs

**Financial Data:**
- **Yahoo Finance**: Historical prices, technical indicators
- **FinnHub**: Company news, insider trading, SEC filings
- **SimFin**: Financial statements (balance sheet, income, cash flow)

**Sentiment Data:**
- **Reddit**: Social media sentiment from financial subreddits
- **Google News**: Global news and macroeconomic events
- **OpenAI Web Search**: Real-time news and fundamental analysis

**Technical Analysis:**
- **StockStats**: Technical indicators (MACD, RSI, Bollinger Bands, etc.)
- **Custom Indicators**: Momentum, volatility, and trend analysis

### Agent Interaction Flow

```mermaid
graph TD
    A[Market Data Input] --> B[Analyst Team]
    B --> C[Market Analyst]
    B --> D[News Analyst]
    B --> E[Social Analyst]
    B --> F[Fundamentals Analyst]

    C --> G[Research Team]
    D --> G
    E --> G
    F --> G

    G --> H[Bull Researcher]
    G --> I[Bear Researcher]
    H --> J[Research Manager]
    I --> J

    J --> K[Trader Agent]
    K --> L[Risk Management]
    L --> M[Risky Analyst]
    L --> N[Neutral Analyst]
    L --> O[Safe Analyst]

    M --> P[Risk Manager]
    N --> P
    O --> P

    P --> Q[Portfolio Manager]
    Q --> R[Final Decision: BUY/SELL/HOLD]
```

### Memory & Learning System

Each agent maintains persistent memory to learn from past decisions:

- **Bull/Bear Memory**: Stores successful/failed investment arguments
- **Trader Memory**: Remembers profitable/unprofitable trading strategies
- **Risk Manager Memory**: Tracks risk assessment accuracy
- **Investment Judge Memory**: Records decision-making patterns

**Memory Structure:**
```python
{
    "situation": "Market conditions and analysis context",
    "decision": "BUY/SELL/HOLD recommendation",
    "reasoning": "Detailed rationale for the decision",
    "outcome": "Actual profit/loss result",
    "lessons": "Key learnings for future decisions"
}
```

### Configuration Management

**File Structure:**
```
tradingagents/
├── default_config.py          # Main configuration file
├── dataflows/
│   ├── config.py              # Data source configuration
│   ├── interface.py           # API interface layer
│   └── *_utils.py             # Individual API utilities
├── agents/
│   ├── analysts/              # Analysis agents
│   ├── researchers/           # Bull/Bear debate agents
│   ├── risk_mgmt/             # Risk assessment agents
│   ├── managers/              # Decision management agents
│   └── trader/                # Trading execution agent
└── graph/
    ├── trading_graph.py       # Main orchestration
    ├── setup.py               # Graph configuration
    └── conditional_logic.py   # Flow control logic
```

## 🔧 Troubleshooting & FAQ

### Common Issues

**1. API Key Errors**
```
Error: DEEPSEEK_API_KEY environment variable is required
```
**Solution:** Ensure environment variables are properly set:
```bash
# Check if variables are set
echo $DEEPSEEK_API_KEY
echo $FINNHUB_API_KEY

# Set them if missing
export DEEPSEEK_API_KEY="your_deepseek_key_here"
export FINNHUB_API_KEY="your_finnhub_key_here"

# Or create .env file
echo "DEEPSEEK_API_KEY=your_key_here" > .env
echo "FINNHUB_API_KEY=your_key_here" >> .env
```

**2. Rate Limit Errors**
```
Error: Rate limit exceeded for FinnHub API
```
**Solution:** The framework automatically handles rate limits, but you can:
- Use `online_tools: False` for cached data
- Reduce `max_debate_rounds` to minimize API calls
- Wait a few minutes and retry

**3. Import Errors**
```
ModuleNotFoundError: No module named 'tradingagents'
```
**Solution:** Ensure you're in the correct directory and dependencies are installed:
```bash
cd TradingAgents
pip install -r requirements.txt
python -m cli.main  # Use module syntax
```

**4. Memory/Performance Issues**
```
Process killed due to memory usage
```
**Solution:**
- Use lighter LLM models (`gpt-4o-mini`, `gpt-4.1-nano`)
- Reduce debate rounds in configuration
- Close other applications to free memory

### Frequently Asked Questions

**Q: How much do API calls cost?**
A: Costs depend on your configuration:
- **Budget setup** (`gpt-4.1-nano`): ~$0.10-0.50 per analysis
- **Recommended setup** (`gpt-4o-mini` + `o4-mini`): ~$1-5 per analysis
- **Production setup** (`gpt-4o` + `o1-preview`): ~$10-50 per analysis

**Q: Can I use other LLM providers besides DeepSeek?**
A: Yes! The framework supports both DeepSeek and OpenAI models:
- **DeepSeek** (Recommended): Set `DEEPSEEK_API_KEY` for cost-effective analysis
- **OpenAI** (Fallback): Set `OPENAI_API_KEY` if DeepSeek is not available
- **Custom providers**: Can be added by implementing the LangChain ChatModel interface

**Q: How accurate are the trading recommendations?**
A: TradingAgents is a research framework, not financial advice. Accuracy depends on:
- Market conditions and data quality
- LLM model capabilities and configuration
- Debate depth and analyst selection
- Historical context and memory learning

**Q: Can I backtest strategies with historical data?**
A: Yes! Use historical dates in the `propagate()` function:
```python
# Backtest on historical date
_, decision = ta.propagate("AAPL", "2023-01-15")
```

**Q: How do I add custom analysts or modify existing ones?**
A: Create new analyst functions following the existing pattern:
```python
def create_custom_analyst(llm, toolkit):
    def custom_analyst_node(state):
        # Your custom analysis logic
        return {"messages": [result], "custom_report": analysis}
    return custom_analyst_node
```

**Q: What data is cached vs. real-time?**
A:
- `online_tools: True`: Real-time data from APIs
- `online_tools: False`: Cached data from Tauric TradingDB
- Cached data covers 2015-2025 for major stocks

### Performance Optimization

**For Faster Analysis:**
- Use `gpt-4o-mini` for quick_think_llm
- Set `max_debate_rounds: 1`
- Select fewer analysts (e.g., just "market")
- Use `online_tools: False` for cached data

**For Better Accuracy:**
- Use `o1-preview` for deep_think_llm
- Increase `max_debate_rounds: 3+`
- Include all analysts
- Use `online_tools: True` for real-time data

**For Cost Efficiency:**
- Use `gpt-4.1-nano` for both LLMs
- Minimize debate rounds
- Test with paper trading first

## Contributing

We welcome contributions from the community! Whether it's fixing a bug, improving documentation, or suggesting a new feature, your input helps make this project better. If you are interested in this line of research, please consider joining our open-source financial AI research community [Tauric Research](https://tauric.ai/).

### How to Contribute

1. **Fork the repository** and create a feature branch
2. **Add tests** for any new functionality
3. **Update documentation** including README and docstrings
4. **Submit a pull request** with clear description of changes

### Development Setup

```bash
# Clone your fork
git clone https://github.com/your-username/TradingAgents.git
cd TradingAgents

# Create development environment
conda create -n tradingagents-dev python=3.13
conda activate tradingagents-dev

# Install in development mode
pip install -e .
pip install -r requirements.txt

# Run tests
python -m pytest tests/
```

## Citation

Please reference our work if you find *TradingAgents* provides you with some help :)

```
@misc{xiao2025tradingagentsmultiagentsllmfinancial,
      title={TradingAgents: Multi-Agents LLM Financial Trading Framework}, 
      author={Yijia Xiao and Edward Sun and Di Luo and Wei Wang},
      year={2025},
      eprint={2412.20138},
      archivePrefix={arXiv},
      primaryClass={q-fin.TR},
      url={https://arxiv.org/abs/2412.20138}, 
}
```
