#!/usr/bin/env python3
"""
数据设置脚本

用于下载和准备回测所需的股票数据。
"""

import os
import sys
from pathlib import Path
import pandas as pd
import yfinance as yf
from typing import Optional
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from tradingagents.default_config import DEFAULT_CONFIG


class DataSetup:
    """数据设置类"""
    
    def __init__(self):
        """初始化数据设置"""
        self.data_dir = Path(DEFAULT_CONFIG["data_dir"])
        self.price_data_dir = self.data_dir / "market_data" / "price_data"
        
    def setup_directories(self):
        """创建必要的目录结构"""
        directories = [
            self.data_dir,
            self.data_dir / "market_data",
            self.data_dir / "market_data" / "price_data",
            self.data_dir / "news_data",
            self.data_dir / "fundamental_data"
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            print(f"✅ 创建目录: {directory}")    def download_stock_data(self, symbols: list, start_date: str = "2015-01-01", 
                           end_date: Optional[str] = None) -> None:
        """
        下载股票数据
        
        Args:
            symbols: 股票代码列表
            start_date: 开始日期
            end_date: 结束日期
        """
        if end_date is None:
            end_date = datetime.now().strftime("%Y-%m-%d")
        
        print(f"📊 开始下载股票数据...")
        print(f"   时间范围: {start_date} ~ {end_date}")
        print(f"   股票列表: {', '.join(symbols)}")
        
        for symbol in symbols:
            try:
                print(f"📥 下载 {symbol} 数据...")
                
                # 下载数据
                ticker = yf.Ticker(symbol)
                data = ticker.history(start=start_date, end=end_date)
                
                if data.empty:
                    print(f"⚠️  {symbol}: 无数据")
                    continue
                
                # 重置索引，使Date成为一列
                data.reset_index(inplace=True)
                
                # 保存数据
                filename = f"{symbol}-YFin-data-{start_date}-{end_date}.csv"
                filepath = self.price_data_dir / filename
                data.to_csv(filepath, index=False)
                
                print(f"✅ {symbol}: {len(data)} 条记录 -> {filename}")
                
            except Exception as e:
                print(f"❌ {symbol}: 下载失败 - {e}")
    
    def verify_data(self, symbols: list, required_date_range: tuple = None) -> dict:
        """
        验证数据完整性
        
        Args:
            symbols: 股票代码列表
            required_date_range: 所需日期范围 (start_date, end_date)
            
        Returns:
            验证报告
        """
        print("🔍 验证数据完整性...")
        
        report = {
            "available": [],
            "missing": [],
            "date_issues": []
        }
        
        for symbol in symbols:
            # 查找数据文件
            pattern = f"{symbol}-YFin-data-*.csv"
            data_files = list(self.price_data_dir.glob(pattern))
            
            if not data_files:
                report["missing"].append(symbol)
                print(f"❌ {symbol}: 数据文件不存在")
                continue
            
            # 检查最新的数据文件
            latest_file = max(data_files, key=lambda x: x.stat().st_mtime)
            
            try:
                df = pd.read_csv(latest_file)
                if df.empty:
                    report["missing"].append(symbol)
                    print(f"❌ {symbol}: 数据文件为空")
                    continue
                
                # 检查日期列
                if 'Date' not in df.columns:
                    report["missing"].append(symbol)
                    print(f"❌ {symbol}: 缺少Date列")
                    continue
                
                df['Date'] = pd.to_datetime(df['Date'])
                min_date = df['Date'].min().strftime('%Y-%m-%d')
                max_date = df['Date'].max().strftime('%Y-%m-%d')
                
                # 检查日期范围
                if required_date_range:
                    req_start, req_end = required_date_range
                    if min_date > req_start or max_date < req_end:
                        report["date_issues"].append({
                            "symbol": symbol,
                            "available_range": f"{min_date} ~ {max_date}",
                            "required_range": f"{req_start} ~ {req_end}"
                        })
                
                report["available"].append(symbol)
                print(f"✅ {symbol}: {len(df)} 条记录 ({min_date} ~ {max_date})")
                
            except Exception as e:
                report["missing"].append(symbol)
                print(f"❌ {symbol}: 读取失败 - {e}")
        
        return report
    
    def setup_sample_data(self):
        """设置示例数据（用于快速测试）"""
        print("🎯 设置示例数据...")
        
        # 常用股票列表
        sample_symbols = ["AAPL", "MSFT", "GOOGL", "TSLA", "NVDA", "AMZN"]
        
        # 设置目录
        self.setup_directories()
        
        # 下载数据
        self.download_stock_data(
            symbols=sample_symbols,
            start_date="2020-01-01"
        )
        
        # 验证数据
        report = self.verify_data(sample_symbols)
        
        print("\n📋 数据设置完成:")
        print(f"   可用股票: {len(report['available'])}")
        print(f"   缺失股票: {len(report['missing'])}")
        if report['date_issues']:
            print(f"   日期问题: {len(report['date_issues'])}")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="TradingAgents 数据设置工具")
    parser.add_argument(
        "--setup-sample", 
        action="store_true", 
        help="设置示例数据"
    )
    parser.add_argument(
        "--symbols", 
        nargs="+", 
        help="指定股票代码列表"
    )
    parser.add_argument(
        "--start-date", 
        default="2020-01-01", 
        help="开始日期 (YYYY-MM-DD)"
    )
    parser.add_argument(
        "--end-date", 
        help="结束日期 (YYYY-MM-DD)"
    )
    parser.add_argument(
        "--verify", 
        action="store_true", 
        help="仅验证现有数据"
    )
    
    args = parser.parse_args()
    
    data_setup = DataSetup()
    
    try:
        if args.setup_sample:
            data_setup.setup_sample_data()
            
        elif args.verify:
            symbols = args.symbols or ["AAPL", "MSFT", "GOOGL"]
            data_setup.verify_data(symbols)
            
        elif args.symbols:
            data_setup.setup_directories()
            data_setup.download_stock_data(
                symbols=args.symbols,
                start_date=args.start_date,
                end_date=args.end_date
            )
            data_setup.verify_data(args.symbols)
            
        else:
            print("请指定操作: --setup-sample, --verify, 或 --symbols")
            parser.print_help()
            
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
