# TradingAgents 在线数据获取指南

## 概述

本指南介绍如何使用TradingAgents系统的在线数据获取工具，通过调用各种在线API获取四个分析师代理所需的数据并保存到本地。

## 工具概览

系统提供了两个主要的数据获取工具：

1. **单次数据获取脚本** (`fetch_online_data.py`) - 获取指定时间段和股票的数据
2. **批量数据获取脚本** (`batch_fetch_data.py`) - 使用配置文件批量获取多个时间段的数据

## 环境准备

### 1. API密钥配置

在项目根目录的 `.env` 文件中配置以下API密钥：

```bash
# 主要LLM提供商 (推荐DeepSeek)
DEEPSEEK_API_KEY=your_deepseek_api_key_here

# 金融数据API (必需)
FINNHUB_API_KEY=your_finnhub_api_key_here

# 备用LLM提供商 (可选)
OPENAI_API_KEY=your_openai_api_key_here
```

### 2. API密钥获取地址

- **DeepSeek API**: https://platform.deepseek.com/api_keys
- **FinnHub API**: https://finnhub.io/register (免费版本每分钟60次调用)
- **OpenAI API**: https://platform.openai.com/api-keys

### 3. 依赖安装

确保已安装所有必需的Python包：

```bash
pip install -r requirements.txt
```

## 单次数据获取

### 基本用法

```bash
# 获取单个股票数据
python scripts/fetch_online_data.py --symbols AAPL --start-date 2025-01-01 --end-date 2025-01-31

# 获取多个股票数据
python scripts/fetch_online_data.py --symbols AAPL,MSFT,GOOGL --start-date 2024-01-01 --end-date 2024-01-31
```

### 高级选项

```bash
# 指定自定义数据目录
python scripts/fetch_online_data.py --symbols AAPL --start-date 2025-01-01 --end-date 2025-01-31 --data-dir ./my_data

# 只获取特定类型的数据
python scripts/fetch_online_data.py --symbols AAPL --start-date 2024-01-01 --end-date 2024-01-31 --analysts market,news

# 调整API调用间隔
python scripts/fetch_online_data.py --symbols AAPL --start-date 2024-01-01 --end-date 2024-01-31 --delay 2.0

# 显示详细输出
python scripts/fetch_online_data.py --symbols AAPL --start-date 2024-01-01 --end-date 2024-01-31 --verbose
```

### 参数说明

- `--symbols`: 股票代码，用逗号分隔 (必需)
- `--start-date`: 开始日期，格式 YYYY-MM-DD (必需)
- `--end-date`: 结束日期，格式 YYYY-MM-DD (必需)
- `--analysts`: 指定分析师类型，可选值: market,news,social,fundamentals
- `--data-dir`: 自定义数据保存目录
- `--delay`: API调用间隔时间(秒)，默认1秒
- `--verbose`: 显示详细输出

## 批量数据获取

### 配置文件

首先编辑配置文件 `scripts/data_fetch_config.yaml`：

```yaml
# 股票配置
symbols:
  - "AAPL"
  - "MSFT"
  - "GOOGL"

# 时间段配置
date_ranges:
  - start_date: "2024-01-01"
    end_date: "2024-03-31"
    name: "2024_Q1"
  - start_date: "2024-04-01"
    end_date: "2024-06-30"
    name: "2024_Q2"

# 分析师配置
analysts:
  enabled:
    - "market"
    - "news"
    - "social"
    - "fundamentals"
```

### 执行批量获取

```bash
# 使用默认配置文件
python scripts/batch_fetch_data.py

# 使用自定义配置文件
python scripts/batch_fetch_data.py --config my_config.yaml

# 显示详细输出
python scripts/batch_fetch_data.py --config my_config.yaml --verbose
```

## 数据获取详情

### 1. 市场分析师数据

**数据来源**: Yahoo Finance, StockStats

**获取内容**:
- 股票价格数据 (开盘价、收盘价、最高价、最低价、成交量)
- 技术指标 (RSI, MACD, 布林带, 移动平均线等)

**保存位置**:
```
data/
├── market_data/
│   ├── price_data/
│   │   └── {SYMBOL}-YFin-data-{start_date}-{end_date}.csv
│   └── indicators/
│       └── {SYMBOL}-indicators-{date}.json
```

### 2. 新闻分析师数据

**数据来源**: OpenAI API, Google News

**获取内容**:
- 全球宏观经济新闻
- 股票相关新闻

**保存位置**:
```
data/
└── news_data/
    ├── global/
    │   └── global-news-{date}.txt
    └── google/
        └── {SYMBOL}-google-news-{date}.txt
```

### 3. 社交媒体分析师数据

**数据来源**: OpenAI API (社交媒体搜索)

**获取内容**:
- 股票相关社交媒体讨论和情绪

**保存位置**:
```
data/
└── social_data/
    └── {SYMBOL}-social-{date}.txt
```

### 4. 基本面分析师数据

**数据来源**: OpenAI API (基本面信息搜索)

**获取内容**:
- 公司基本面信息
- 财务指标 (PE比率、PS比率、现金流等)

**保存位置**:
```
data/
└── fundamentals_data/
    └── {SYMBOL}-fundamentals-{date}.txt
```

## 数据质量和成本控制

### 建议的使用策略

1. **开发测试阶段**:
   - 使用较短的时间范围 (1-7天)
   - 选择少量股票进行测试
   - 设置较长的API调用间隔 (2-3秒)

2. **生产数据获取**:
   - 分批获取数据，避免一次性大量调用
   - 监控API使用量和成本
   - 定期备份重要数据

### API调用成本估算

以获取1个股票1个月数据为例：

- **市场数据**: ~5次API调用 (价格数据 + 技术指标)
- **新闻数据**: ~2次API调用 (全球新闻 + 股票新闻)
- **社交媒体数据**: ~1次API调用
- **基本面数据**: ~1次API调用

**总计**: 约9次API调用/股票/月

## 故障排除

### 常见问题

1. **API密钥错误**
   ```
   ❌ 缺少以下API密钥: DEEPSEEK_API_KEY (用于LLM分析)
   ```
   **解决方案**: 检查 `.env` 文件中的API密钥配置

2. **网络连接问题**
   ```
   ❌ 获取数据失败: Connection timeout
   ```
   **解决方案**: 检查网络连接，增加 `--delay` 参数值

3. **API调用限制**
   ```
   ❌ API调用频率过高
   ```
   **解决方案**: 增加 `--delay` 参数值，或等待一段时间后重试

4. **数据格式错误**
   ```
   ❌ 日期格式错误，请使用 YYYY-MM-DD 格式
   ```
   **解决方案**: 确保日期格式正确

### 调试技巧

1. **使用详细输出模式**:
   ```bash
   python scripts/fetch_online_data.py --symbols AAPL --start-date 2024-01-01 --end-date 2024-01-31 --verbose
   ```

2. **检查生成的文件**:
   ```bash
   # 查看数据目录结构
   tree data/
   
   # 检查汇总文件
   cat data/data_summary_2024-01-31.json
   ```

3. **测试单个分析师**:
   ```bash
   # 只测试市场分析师
   python scripts/fetch_online_data.py --symbols AAPL --start-date 2024-01-01 --end-date 2024-01-31 --analysts market
   ```

## 最佳实践

1. **数据获取策略**:
   - 优先获取近期数据，历史数据可以分批获取
   - 对于重要股票，可以增加获取频率
   - 定期更新数据以保持时效性

2. **成本控制**:
   - 合理设置API调用间隔
   - 避免重复获取相同数据
   - 监控API使用量

3. **数据管理**:
   - 定期清理过期数据
   - 建立数据备份机制
   - 验证数据完整性

4. **错误处理**:
   - 实现重试机制
   - 记录失败的请求
   - 监控数据质量

---

*本指南基于TradingAgents v1.0编写，如有更新请参考最新版本的代码实现。*
