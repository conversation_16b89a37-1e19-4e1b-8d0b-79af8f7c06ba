# TradingAgents 分析师代理数据来源说明文档

## 概述

TradingAgents 系统包含四个专业的分析师代理，每个代理都有特定的数据来源和获取方式。本文档详细说明了每个分析师代理的数据来源、获取方法以及配置要求。

## 分析师代理概览

系统包含以下四个分析师代理：
1. **市场分析师** (Market Analyst) - 技术分析和市场数据
2. **新闻分析师** (News Analyst) - 新闻和媒体信息
3. **社交媒体分析师** (Social Media Analyst) - 社交媒体情绪和讨论
4. **基本面分析师** (Fundamentals Analyst) - 财务报表和公司基本面

## 数据获取模式

系统支持两种数据获取模式：
- **在线模式** (`online_tools: true`) - 实时获取数据，需要API密钥
- **离线模式** (`online_tools: false`) - 使用预下载的本地数据

## 1. 市场分析师 (Market Analyst)

### 数据来源
- **Yahoo Finance** - 股票价格和交易数据
- **StockStats** - 技术指标计算

### 在线模式工具
- `get_YFin_data_online` - 实时获取Yahoo Finance数据
- `get_stockstats_indicators_report_online` - 在线计算技术指标

### 离线模式工具
- `get_YFin_data` - 从本地CSV文件读取价格数据
- `get_stockstats_indicators_report` - 基于本地数据计算技术指标

### 数据存储位置
```
data/
├── market_data/
│   └── price_data/
│       └── {SYMBOL}-YFin-data-2015-01-01-2025-03-25.csv
└── dataflows/
    └── data_cache/
        └── {SYMBOL}-YFin-data-{start_date}-{end_date}.csv
```

### 技术指标支持
支持多种技术指标分析，包括：
- 移动平均线 (MA, EMA)
- 相对强弱指数 (RSI)
- MACD
- 布林带 (Bollinger Bands)
- 等其他StockStats库支持的指标

### 配置要求
- 离线模式：需要预下载股票价格数据
- 在线模式：自动从Yahoo Finance获取数据

## 2. 新闻分析师 (News Analyst)

### 数据来源
- **OpenAI API** (在线模式) - 全球新闻搜索
- **FinnHub API** - 金融新闻
- **Google News** - 新闻搜索
- **Reddit** - 社交媒体新闻讨论

### 在线模式工具
- `get_global_news_openai` - 使用OpenAI API搜索全球新闻
- `get_google_news` - Google新闻搜索

### 离线模式工具
- `get_finnhub_news` - FinnHub新闻数据
- `get_reddit_news` - Reddit新闻讨论
- `get_google_news` - Google新闻搜索（网页抓取）

### 数据存储位置
```
data/
├── finnhub_data/
│   └── news_data/
│       └── {SYMBOL}_data_formatted.json
└── reddit_data/
    └── global_news/
        └── {subreddit}/
            └── {date}.jsonl
```

### API配置要求
- **FinnHub API**: 需要`FINNHUB_API_KEY`环境变量
- **OpenAI API**: 需要`OPENAI_API_KEY`或`DEEPSEEK_API_KEY`
- **Google News**: 无需API密钥，使用网页抓取

### 数据获取方式
- FinnHub: REST API调用，获取结构化新闻数据
- Google News: 网页抓取，解析HTML内容
- Reddit: 预处理的JSONL格式文件
- OpenAI: 通过LLM API进行新闻搜索

## 3. 社交媒体分析师 (Social Media Analyst)

### 数据来源
- **OpenAI API** (在线模式) - 社交媒体搜索
- **Reddit** - 股票相关讨论和情绪

### 在线模式工具
- `get_stock_news_openai` - 使用OpenAI API搜索股票相关社交媒体内容

### 离线模式工具
- `get_reddit_stock_info` - Reddit股票讨论数据

### 数据存储位置
```
data/
└── reddit_data/
    └── company_news/
        └── {subreddit}/
            └── {date}.jsonl
```

### 支持的Reddit子版块
系统预配置了多个金融相关的subreddit：
- r/investing
- r/stocks
- r/SecurityAnalysis
- r/ValueInvesting
- 等其他投资相关社区

### 数据处理方式
- Reddit数据按日期和子版块组织
- 自动过滤与目标股票相关的讨论
- 提取帖子标题、内容、投票数等信息
- 支持公司名称和股票代码的智能匹配

## 4. 基本面分析师 (Fundamentals Analyst)

### 数据来源
- **OpenAI API** (在线模式) - 基本面信息搜索
- **SimFin** - 财务报表数据
- **FinnHub** - 内部人交易和情绪数据

### 在线模式工具
- `get_fundamentals_openai` - 使用OpenAI API获取基本面信息

### 离线模式工具
- `get_simfin_balance_sheet` - 资产负债表
- `get_simfin_cashflow` - 现金流量表
- `get_simfin_income_stmt` - 利润表
- `get_finnhub_company_insider_sentiment` - 内部人情绪
- `get_finnhub_company_insider_transactions` - 内部人交易

### 数据存储位置
```
data/
├── fundamental_data/
│   └── simfin_data_all/
│       ├── balance_sheet/
│       │   └── companies/us/us-balance-{freq}.csv
│       ├── cash_flow/
│       │   └── companies/us/us-cashflow-{freq}.csv
│       └── income_statements/
│           └── companies/us/us-income-{freq}.csv
└── finnhub_data/
    ├── insider_senti/
    │   └── {SYMBOL}_data_formatted.json
    └── insider_trans/
        └── {SYMBOL}_data_formatted.json
```

### 财务数据特点
- **SimFin数据**: 标准化的财务报表数据，支持年度和季度频率
- **FinnHub内部人数据**: 包含内部人交易记录和情绪指标
- **数据时间范围**: 根据交易日期自动筛选相关时间段的数据

## 环境配置要求

### 必需的API密钥
```bash
# 主要LLM提供商 (推荐DeepSeek)
DEEPSEEK_API_KEY=your_deepseek_api_key_here

# 金融数据API
FINNHUB_API_KEY=your_finnhub_api_key_here

# 备用LLM提供商 (可选)
OPENAI_API_KEY=your_openai_api_key_here
```

### API获取地址
- **DeepSeek API**: https://platform.deepseek.com/api_keys
- **FinnHub API**: https://finnhub.io/register (免费版本每分钟60次调用)
- **OpenAI API**: https://platform.openai.com/api-keys

## 数据缓存机制

### 缓存策略
- **在线模式**: 自动缓存API响应到本地文件
- **离线模式**: 直接读取预下载的数据文件
- **缓存位置**: `tradingagents/dataflows/data_cache/`

### 缓存优势
- 减少API调用次数和成本
- 提高数据获取速度
- 支持离线回测和分析
- 确保数据一致性

## 使用建议

### 开发和测试阶段
- 推荐使用**离线模式**以节省API成本
- 预先下载必要的历史数据
- 使用较短的时间范围进行测试

### 生产环境
- 可以使用**在线模式**获取最新数据
- 合理配置API调用频率限制
- 监控API使用量和成本

### 数据质量保证
- 定期更新本地数据文件
- 验证API密钥的有效性
- 检查数据完整性和时间范围覆盖

## 故障排除

### 常见问题
1. **API密钥无效**: 检查环境变量配置
2. **数据文件缺失**: 运行数据下载脚本
3. **网络连接问题**: 检查网络设置和代理配置
4. **数据格式错误**: 验证数据文件格式和结构

### 调试建议
- 启用详细日志记录
- 检查数据文件路径和权限
- 验证API响应格式
- 测试单个工具的功能

---

*本文档基于TradingAgents v1.0编写，如有更新请参考最新版本的代码实现。*
