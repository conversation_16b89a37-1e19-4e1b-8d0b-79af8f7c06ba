"""
TradingAgents 回测框架使用示例

这个示例展示了如何使用回测框架进行各种类型的回测实验。
"""

import sys
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent))

from tradingagents.backtesting import (
    BacktestConfig, ModelConfig, AgentConfig,
    BacktestEngine, PerformanceAnalyzer, ResultVisualizer, ExperimentManager
)


def example_1_quick_backtest():
    """示例1: 快速回测"""
    print("🚀 示例1: 快速回测")
    print("=" * 50)
    
    # 创建简单配置
    config = BacktestConfig(
        experiment_name="quick_test_example",
        description="快速回测示例 - 单股票短期测试",
        start_date="2024-01-01",
        end_date="2024-01-31",
        symbols=["AAPL"],
        model_config=ModelConfig(
            provider="deepseek",
            deep_think_model="deepseek-reasoner",
            quick_think_model="deepseek-chat"
        ),
        agent_config=AgentConfig(
            selected_analysts=["market"],
            max_debate_rounds=1
        ),
        online_tools=False
    )
    
    # 运行回测
    experiment_manager = ExperimentManager()
    engine = BacktestEngine(experiment_manager)
    
    try:
        result = engine.run_backtest(config)
        print(f"✅ 快速回测完成: {result['experiment_id']}")
        
        # 打印基本统计
        summary = result['summary']
        print(f"总分析次数: {summary['execution_summary']['total_analyses']}")
        print(f"成功率: {summary['execution_summary']['success_rate']:.2%}")
        
    except Exception as e:
        print(f"❌ 回测失败: {e}")


def example_2_comprehensive_backtest():
    """示例2: 全面回测"""
    print("\n🔬 示例2: 全面回测")
    print("=" * 50)
    
    # 创建全面配置
    config = BacktestConfig(
        experiment_name="comprehensive_test_example",
        description="全面回测示例 - 多股票多分析师",
        start_date="2024-01-01",
        end_date="2024-03-31",
        symbols=["AAPL", "MSFT", "GOOGL"],
        model_config=ModelConfig(
            provider="deepseek",
            deep_think_model="deepseek-reasoner",
            quick_think_model="deepseek-chat",
            temperature=0.1
        ),
        agent_config=AgentConfig(
            selected_analysts=["market", "news", "fundamentals"],
            max_debate_rounds=1,
            max_risk_discuss_rounds=1
        ),
        online_tools=False,
        output_dir="backtest_results/comprehensive_example"
    )
    
    # 运行回测
    experiment_manager = ExperimentManager()
    engine = BacktestEngine(experiment_manager)
    
    try:
        result = engine.run_backtest(config)
        experiment_id = result['experiment_id']
        print(f"✅ 全面回测完成: {experiment_id}")
        
        # 生成详细分析
        generate_detailed_analysis(experiment_id, result)
        
    except Exception as e:
        print(f"❌ 回测失败: {e}")


def example_3_model_comparison():
    """示例3: 模型对比"""
    print("\n⚖️  示例3: 模型对比")
    print("=" * 50)
    
    # 配置1: 低温度
    config1 = BacktestConfig(
        experiment_name="model_comparison_low_temp",
        description="模型对比 - 低温度设置",
        start_date="2024-01-01",
        end_date="2024-02-29",
        symbols=["AAPL", "MSFT"],
        model_config=ModelConfig(
            provider="deepseek",
            temperature=0.1
        ),
        agent_config=AgentConfig(selected_analysts=["market", "news"]),
        online_tools=False
    )
    
    # 配置2: 高温度
    config2 = BacktestConfig(
        experiment_name="model_comparison_high_temp",
        description="模型对比 - 高温度设置",
        start_date="2024-01-01",
        end_date="2024-02-29",
        symbols=["AAPL", "MSFT"],
        model_config=ModelConfig(
            provider="deepseek",
            temperature=0.7
        ),
        agent_config=AgentConfig(selected_analysts=["market", "news"]),
        online_tools=False
    )
    
    experiment_manager = ExperimentManager()
    engine = BacktestEngine(experiment_manager)
    
    results = []
    
    for i, config in enumerate([config1, config2], 1):
        try:
            print(f"运行配置 {i}: {config.experiment_name}")
            result = engine.run_backtest(config)
            results.append(result)
            print(f"✅ 配置 {i} 完成: {result['experiment_id']}")
        except Exception as e:
            print(f"❌ 配置 {i} 失败: {e}")
    
    # 对比结果
    if len(results) == 2:
        compare_experiments(results)


def generate_detailed_analysis(experiment_id: str, backtest_result: dict):
    """生成详细分析"""
    print(f"\n📊 生成详细分析: {experiment_id}")
    
    # 初始化分析器
    performance_analyzer = PerformanceAnalyzer()
    visualizer = ResultVisualizer()
    
    # 处理结果数据
    results = backtest_result["results"]
    successful_results = [r for r in results if r["success"]]
    
    if not successful_results:
        print("⚠️  没有成功的分析结果")
        return
    
    # 创建交易DataFrame
    import pandas as pd
    trades_df = pd.DataFrame([
        {
            "symbol": r["symbol"],
            "date": r["date"],
            "decision": r["decision"],
            "success": r["success"]
        }
        for r in successful_results
    ])
    
    print(f"成功交易记录: {len(trades_df)}")
    print(f"决策分布:")
    decision_counts = trades_df['decision'].value_counts()
    for decision, count in decision_counts.items():
        print(f"  {decision}: {count}")


def compare_experiments(results: list):
    """对比实验结果"""
    print(f"\n📈 实验对比")
    print("=" * 50)
    
    for i, result in enumerate(results, 1):
        summary = result['summary']
        execution = summary['execution_summary']
        
        print(f"实验 {i}: {summary['experiment_info']['name']}")
        print(f"  成功率: {execution['success_rate']:.2%}")
        print(f"  总分析: {execution['total_analyses']}")
        print(f"  决策分布: {summary.get('decision_distribution', {})}")
        print()


def example_4_configuration_management():
    """示例4: 配置管理"""
    print("\n⚙️  示例4: 配置管理")
    print("=" * 50)
    
    # 创建配置
    config = BacktestConfig(
        experiment_name="config_management_example",
        description="配置管理示例",
        start_date="2024-01-01",
        end_date="2024-01-31",
        symbols=["AAPL"],
        model_config=ModelConfig(provider="deepseek"),
        agent_config=AgentConfig(selected_analysts=["market"])
    )
    
    # 保存配置到文件
    config_file = "example_config.json"
    config.save_to_file(config_file)
    print(f"✅ 配置已保存: {config_file}")
    
    # 从文件加载配置
    loaded_config = BacktestConfig.load_from_file(config_file)
    print(f"✅ 配置已加载: {loaded_config.experiment_name}")
    
    # 验证配置
    try:
        loaded_config.validate()
        print("✅ 配置验证通过")
    except Exception as e:
        print(f"❌ 配置验证失败: {e}")
    
    # 清理
    import os
    if os.path.exists(config_file):
        os.remove(config_file)


def example_5_experiment_management():
    """示例5: 实验管理"""
    print("\n🧪 示例5: 实验管理")
    print("=" * 50)
    
    experiment_manager = ExperimentManager()
    
    # 列出现有实验
    print("现有实验:")
    experiment_manager.print_experiment_summary()
    
    # 创建新实验
    config = BacktestConfig(
        experiment_name="experiment_management_example",
        description="实验管理示例",
        start_date="2024-01-01",
        end_date="2024-01-15",
        symbols=["AAPL"]
    )
    
    experiment_id = experiment_manager.create_experiment(config)
    print(f"✅ 新实验已创建: {experiment_id}")
    
    # 更新实验状态
    experiment_manager.update_experiment_status(experiment_id, "completed")
    print(f"✅ 实验状态已更新: completed")
    
    # 导出实验摘要
    summary_df = experiment_manager.export_experiment_summary("experiments_summary.csv")
    print(f"✅ 实验摘要已导出: {len(summary_df)} 个实验")


def main():
    """运行所有示例"""
    print("🎯 TradingAgents 回测框架示例")
    print("=" * 60)
    
    try:
        # 运行示例
        example_1_quick_backtest()
        example_2_comprehensive_backtest()
        example_3_model_comparison()
        example_4_configuration_management()
        example_5_experiment_management()
        
        print("\n🎉 所有示例运行完成!")
        
    except KeyboardInterrupt:
        print("\n⏹️  用户中断")
    except Exception as e:
        print(f"\n❌ 示例运行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
