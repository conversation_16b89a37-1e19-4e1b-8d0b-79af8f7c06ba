#!/usr/bin/env python3
"""
社交媒体数据获取器测试脚本

快速测试社交媒体数据获取功能是否正常工作
"""

import sys
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from scripts.social_media_data_fetcher import SocialMediaDataFetcher
    print("✅ 成功导入社交媒体数据获取器")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)


def test_environment():
    """测试环境配置"""
    print("\n🔍 测试环境配置")
    print("=" * 40)
    
    fetcher = SocialMediaDataFetcher(debug=True)
    
    # 检查在线模式可用性
    if fetcher.online_available:
        print("✅ 在线模式可用")
    else:
        print("⚠️ 在线模式不可用 - 请检查API密钥配置")
    
    # 检查数据目录
    reddit_dir = fetcher.data_dir / "reddit_data"
    if reddit_dir.exists():
        print("✅ Reddit数据目录存在")
        
        # 检查company_news目录
        company_news_dir = reddit_dir / "company_news"
        if company_news_dir.exists():
            subreddits = [d.name for d in company_news_dir.iterdir() if d.is_dir()]
            print(f"📋 可用subreddit: {subreddits}")
        else:
            print("⚠️ company_news目录不存在")
    else:
        print("⚠️ Reddit数据目录不存在")
    
    return fetcher


def test_quality_assessment():
    """测试内容质量评估"""
    print("\n⭐ 测试内容质量评估")
    print("=" * 40)
    
    fetcher = SocialMediaDataFetcher(debug=False)
    
    test_cases = [
        ("空内容", ""),
        ("短内容", "AAPL股价上涨"),
        ("中等内容", "苹果公司股票今日表现强劲，投资者对其新产品发布表示乐观。技术分析显示RSI指标处于健康水平，移动平均线呈上升趋势。社区讨论热烈，大多数投资者持看涨观点。"),
        ("高质量内容", """
### AAPL 股票分析

**投资者情绪:**
- 看涨: 65% 
- 看跌: 20%
- 中性: 15%

**技术分析讨论:**
- RSI: 58 (健康水平)
- MA20: 上升趋势
- 成交量: 放大

**社区热点:**
1. iPhone新品发布
2. 服务业务增长
3. 中国市场表现

投资者普遍对苹果长期前景保持乐观态度。
        """),
        ("指导性文本", "I cannot search for real-time social media data as I don't have access to current information.")
    ]
    
    for name, content in test_cases:
        score = fetcher._assess_content_quality(content)
        print(f"{name}: {score:.1f}/10 (长度: {len(content)})")
    
    print("✅ 质量评估测试完成")


def test_offline_fetch():
    """测试离线数据获取"""
    print("\n💾 测试离线数据获取")
    print("=" * 40)
    
    fetcher = SocialMediaDataFetcher(debug=True)
    
    # 测试常见股票
    test_stocks = ["AAPL", "TSLA", "NVDA"]
    test_date = "2024-05-20"
    
    for ticker in test_stocks:
        print(f"\n测试 {ticker}...")
        result = fetcher.fetch_offline_data(ticker, test_date)
        
        if result['success']:
            content_length = result.get('content_length', 0)
            posts_found = result.get('posts_found', 0)
            quality_score = result.get('quality_score', 0)
            
            print(f"  ✅ 成功 - {content_length}字符, {posts_found}帖子, 质量{quality_score:.1f}/10")
        else:
            error = result.get('error', 'Unknown error')
            print(f"  ❌ 失败 - {error}")


def test_online_fetch():
    """测试在线数据获取"""
    print("\n🌐 测试在线数据获取")
    print("=" * 40)
    
    fetcher = SocialMediaDataFetcher(debug=True)
    
    if not fetcher.online_available:
        print("⚠️ 跳过在线测试 - API不可用")
        return
    
    # 测试一个股票
    ticker = "AAPL"
    test_date = "2024-05-20"
    
    print(f"测试 {ticker} 在线获取...")
    result = fetcher.fetch_online_data(ticker, test_date)
    
    if result['success']:
        content_length = result.get('content_length', 0)
        quality_score = result.get('quality_score', 0)
        model = result.get('model', 'Unknown')
        response_time = result.get('api_response_time', 0)
        
        print(f"✅ 成功 - {content_length}字符, 质量{quality_score:.1f}/10")
        print(f"   模型: {model}, 响应时间: {response_time:.2f}秒")
        
        # 显示内容预览
        content = result.get('content', '')
        if content:
            print(f"   预览: {content[:100]}...")
    else:
        error = result.get('error', 'Unknown error')
        print(f"❌ 失败 - {error}")


def test_save_functionality():
    """测试保存功能"""
    print("\n💾 测试保存功能")
    print("=" * 40)
    
    fetcher = SocialMediaDataFetcher(debug=False)
    
    # 创建测试结果
    test_result = {
        'success': True,
        'method': 'test',
        'ticker': 'TEST',
        'date': '2024-05-20',
        'content': '这是一个测试内容，用于验证保存功能是否正常工作。',
        'content_length': 30,
        'quality_score': 7.5,
        'timestamp': datetime.now().isoformat()
    }
    
    try:
        filepath = fetcher.save_results(test_result)
        print(f"✅ 保存成功: {filepath}")
        
        # 验证文件存在
        if filepath.exists():
            print("✅ 文件确实存在")
            
            # 检查文件大小
            file_size = filepath.stat().st_size
            print(f"📄 文件大小: {file_size} 字节")
        else:
            print("❌ 文件不存在")
            
    except Exception as e:
        print(f"❌ 保存失败: {e}")


def main():
    """主测试函数"""
    print("🧪 社交媒体数据获取器测试")
    print("=" * 50)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 1. 测试环境
        fetcher = test_environment()
        
        # 2. 测试质量评估
        test_quality_assessment()
        
        # 3. 测试离线获取
        test_offline_fetch()
        
        # 4. 测试在线获取
        test_online_fetch()
        
        # 5. 测试保存功能
        test_save_functionality()
        
        print(f"\n🎯 测试完成!")
        print("=" * 50)
        
        # 提供使用建议
        print("\n💡 使用建议:")
        if fetcher.online_available:
            print("✅ 在线模式可用 - 可以获取实时社交媒体数据")
        else:
            print("⚠️ 在线模式不可用 - 请配置API密钥以启用在线功能")
            print("   设置方法: 在.env文件中添加 DEEPSEEK_API_KEY 或 OPENAI_API_KEY")
        
        reddit_dir = Path("data/reddit_data/company_news")
        if reddit_dir.exists():
            print("✅ 离线模式可用 - 可以使用本地Reddit数据")
        else:
            print("⚠️ 离线模式数据不完整 - 请下载Reddit数据以启用离线功能")
        
        print(f"\n📖 详细使用指南请参考: docs/social_media_data_fetcher_guide.md")
        print(f"🚀 开始使用: python scripts/social_media_data_fetcher.py --help")
        
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
