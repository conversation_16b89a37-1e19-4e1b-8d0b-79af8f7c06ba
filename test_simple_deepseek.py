#!/usr/bin/env python3
"""
Simple test for DeepSeek integration with TradingAgents.
This script tests basic functionality without the full workflow.
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_simple_analysis():
    """Test a simple trading analysis with minimal configuration."""
    print("🚀 Simple DeepSeek Trading Analysis Test")
    print("=" * 50)
    
    try:
        from tradingagents.graph.trading_graph import TradingAgentsGraph
        from tradingagents.default_config import DEFAULT_CONFIG
        
        # Create minimal config for testing
        config = DEFAULT_CONFIG.copy()
        config["llm_provider"] = "deepseek"
        config["deep_think_llm"] = "deepseek-chat"      # Use same model for both
        config["quick_think_llm"] = "deepseek-chat"     # to minimize complexity
        config["max_debate_rounds"] = 1                 # Minimal debate
        config["max_risk_discuss_rounds"] = 1           # Minimal risk discussion
        config["online_tools"] = False                  # Use cached data for speed
        
        print("📊 Initializing TradingAgents with minimal configuration...")
        ta = TradingAgentsGraph(
            selected_analysts=["market"],  # Only use market analyst for simplicity
            debug=False,  # Reduce output
            config=config
        )
        
        print("✅ TradingAgents initialized successfully")
        print(f"   LLM Provider: {config['llm_provider']}")
        print(f"   Deep Think Model: {config['deep_think_llm']}")
        print(f"   Quick Think Model: {config['quick_think_llm']}")
        
        # Test with a simple stock
        ticker = "AAPL"
        date = "2024-05-10"
        
        print(f"\n📈 Running analysis for {ticker} on {date}...")
        print("   (This may take 1-2 minutes)")
        
        # Run the analysis
        final_state, decision = ta.propagate(ticker, date)
        
        print(f"\n✅ Analysis completed successfully!")
        print(f"🎯 Trading Decision: {decision}")
        
        # Show some key information from the analysis
        if "market_report" in final_state:
            market_report = final_state["market_report"]
            if isinstance(market_report, str) and len(market_report) > 100:
                print(f"\n📊 Market Analysis Summary:")
                print(f"   {market_report[:200]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Simple analysis failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_deepseek_llm_direct():
    """Test DeepSeek LLM directly."""
    print("\n🤖 Direct DeepSeek LLM Test")
    print("=" * 50)
    
    try:
        from tradingagents.llm.deepseek_llm import create_deepseek_llm
        from langchain_core.messages import HumanMessage
        
        # Create DeepSeek LLM
        llm = create_deepseek_llm(model="deepseek-chat", temperature=0.1)
        print("✅ DeepSeek LLM created successfully")
        
        # Test with a simple financial question
        message = HumanMessage(content="What are the key factors to consider when analyzing a tech stock like Apple?")
        print("📤 Sending test question to DeepSeek...")
        
        response = llm.invoke([message])
        print("✅ DeepSeek response received")
        print(f"📥 Response preview: {response.content[:300]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Direct DeepSeek test failed: {str(e)}")
        return False


def main():
    """Run simple tests."""
    print("🧪 Simple DeepSeek Integration Tests")
    print("=" * 60)
    
    # Check environment
    deepseek_key = os.getenv('DEEPSEEK_API_KEY')
    finnhub_key = os.getenv('FINNHUB_API_KEY')
    
    if not deepseek_key:
        print("❌ DEEPSEEK_API_KEY not found in environment")
        print("   Please set your DeepSeek API key in .env file")
        return
    
    if not finnhub_key:
        print("❌ FINNHUB_API_KEY not found in environment")
        print("   Please set your FinnHub API key in .env file")
        return
    
    print(f"✅ API keys found:")
    print(f"   DeepSeek: {len(deepseek_key)} characters")
    print(f"   FinnHub: {len(finnhub_key)} characters")
    
    # Run tests
    tests = [
        ("Direct DeepSeek LLM", test_deepseek_llm_direct),
        ("Simple Trading Analysis", test_simple_analysis),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🎯 Running {test_name}...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {str(e)}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n📊 Test Results Summary")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("\n🎉 All tests passed! DeepSeek integration is working!")
        print("\nYou can now run:")
        print("  python main.py                    # Full analysis")
        print("  python -m cli.main               # Interactive CLI")
        print("  python examples/deepseek_example.py  # More examples")
    else:
        print("\n⚠️ Some tests failed. Check the error messages above.")


if __name__ == "__main__":
    main()
