#!/usr/bin/env python3
"""
Test script to verify DeepSeek integration with TradingAgents.
This script tests the DeepSeek LLM wrapper and basic functionality.
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_environment():
    """Test if required environment variables are set."""
    print("🔍 Testing Environment Variables...")
    
    deepseek_key = os.getenv('DEEPSEEK_API_KEY')
    finnhub_key = os.getenv('FINNHUB_API_KEY')
    
    if deepseek_key:
        print(f"✅ DEEPSEEK_API_KEY: Found (length: {len(deepseek_key)})")
    else:
        print("❌ DEEPSEEK_API_KEY: Not found")
        
    if finnhub_key:
        print(f"✅ FINNHUB_API_KEY: Found (length: {len(finnhub_key)})")
    else:
        print("❌ FINNHUB_API_KEY: Not found")
    
    return bool(deepseek_key and finnhub_key)


def test_deepseek_llm():
    """Test the DeepSeek LLM wrapper."""
    print("\n🤖 Testing DeepSeek LLM Wrapper...")
    
    try:
        from tradingagents.llm.deepseek_llm import create_deepseek_llm
        
        # Create DeepSeek LLM instance
        llm = create_deepseek_llm(model="deepseek-chat", temperature=0.1)
        print("✅ DeepSeek LLM instance created successfully")
        
        # Test a simple message
        from langchain_core.messages import HumanMessage
        
        test_message = HumanMessage(content="Hello! Can you briefly explain what financial analysis involves?")
        print("📤 Sending test message to DeepSeek...")
        
        response = llm._generate([test_message])
        print("✅ DeepSeek API call successful")
        print(f"📥 Response: {response.generations[0].message.content[:200]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ DeepSeek LLM test failed: {str(e)}")
        return False


def test_trading_agents_config():
    """Test TradingAgents configuration with DeepSeek."""
    print("\n⚙️ Testing TradingAgents Configuration...")
    
    try:
        from tradingagents.default_config import DEFAULT_CONFIG
        
        config = DEFAULT_CONFIG.copy()
        print(f"✅ Default config loaded")
        print(f"   LLM Provider: {config.get('llm_provider', 'not set')}")
        print(f"   Deep Think LLM: {config.get('deep_think_llm', 'not set')}")
        print(f"   Quick Think LLM: {config.get('quick_think_llm', 'not set')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {str(e)}")
        return False


def test_trading_graph_initialization():
    """Test TradingAgentsGraph initialization with DeepSeek."""
    print("\n🔧 Testing TradingAgentsGraph Initialization...")
    
    try:
        from tradingagents.graph.trading_graph import TradingAgentsGraph
        from tradingagents.default_config import DEFAULT_CONFIG
        
        # Create config with DeepSeek
        config = DEFAULT_CONFIG.copy()
        config["llm_provider"] = "deepseek"
        config["deep_think_llm"] = "deepseek-reasoner"
        config["quick_think_llm"] = "deepseek-chat"
        
        # Initialize TradingAgentsGraph
        print("🚀 Initializing TradingAgentsGraph with DeepSeek...")
        ta = TradingAgentsGraph(
            selected_analysts=["market"],  # Use only one analyst for testing
            debug=True,
            config=config
        )
        
        print("✅ TradingAgentsGraph initialized successfully")
        print(f"   Deep thinking LLM type: {type(ta.deep_thinking_llm).__name__}")
        print(f"   Quick thinking LLM type: {type(ta.quick_thinking_llm).__name__}")
        
        return True
        
    except Exception as e:
        print(f"❌ TradingAgentsGraph initialization failed: {str(e)}")
        return False


def main():
    """Run all tests."""
    print("🧪 TradingAgents DeepSeek Integration Test")
    print("=" * 50)
    
    tests = [
        ("Environment Variables", test_environment),
        ("DeepSeek LLM Wrapper", test_deepseek_llm),
        ("TradingAgents Configuration", test_trading_agents_config),
        ("TradingAgentsGraph Initialization", test_trading_graph_initialization),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {str(e)}")
            results.append((test_name, False))
    
    # Summary
    print("\n📊 Test Summary")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("\n🎉 All tests passed! DeepSeek integration is working correctly.")
        print("\nYou can now run:")
        print("  python main.py")
        print("  python -m cli.main")
    else:
        print("\n⚠️ Some tests failed. Please check your configuration.")
        print("\nMake sure you have:")
        print("  1. Set DEEPSEEK_API_KEY in your .env file")
        print("  2. Set FINNHUB_API_KEY in your .env file") 
        print("  3. Installed all dependencies: pip install -r requirements.txt")


if __name__ == "__main__":
    main()
