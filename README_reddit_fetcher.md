# 真实Reddit股票社交媒体数据获取脚本

## 🎯 概述

`test_reddit.py` 是一个能够获取**真实Reddit股票社交媒体数据**的Python脚本。它使用Reddit官方API从多个相关subreddit中搜索和提取股票相关的讨论内容。

## ✨ 主要功能

### 🔍 真实数据获取
- **Reddit API集成**: 使用官方Reddit API获取真实帖子
- **多源搜索**: 同时从10个股票相关subreddit获取数据
- **智能匹配**: 根据股票代码自动匹配公司名称和别名
- **时间过滤**: 支持指定天数的时间范围查询

### 📊 数据分析
- **情绪分析**: 基于关键词的智能情绪分析
- **热度排序**: 按帖子分数和评论数排序
- **质量过滤**: 自动过滤不相关内容

### 💾 结果保存
- **JSON格式**: 机器可读的结构化数据
- **文本格式**: 人类可读的详细报告
- **详细统计**: 包含处理时间、subreddit分布等信息

## 🚀 使用方法

### 基本用法

```bash
# 获取AAPL过去7天的Reddit讨论
python test_reddit.py --ticker AAPL --days 7 --limit 20

# 获取TSLA过去3天的讨论并保存
python test_reddit.py --ticker TSLA --days 3 --save

# 获取NVDA讨论并进行情绪分析
python test_reddit.py --ticker NVDA --days 5 --sentiment
```

### 高级选项

```bash
# 静默模式获取数据
python test_reddit.py --ticker MSFT --days 2 --quiet

# 指定输出文件
python test_reddit.py --ticker GOOGL --days 4 --save --output my_google_data.json

# 完整分析（保存+情绪分析）
python test_reddit.py --ticker META --days 7 --save --sentiment
```

## 📋 参数说明

| 参数 | 简写 | 必需 | 默认值 | 说明 |
|------|------|------|--------|------|
| `--ticker` | `-t` | ✅ | - | 股票代码 (如: AAPL, TSLA) |
| `--days` | `-d` | ❌ | 7 | 向前查找的天数 |
| `--limit` | `-l` | ❌ | 10 | 每个subreddit的最大帖子数 |
| `--save` | | ❌ | False | 保存结果到文件 |
| `--output` | `-o` | ❌ | 自动生成 | 输出文件路径 |
| `--sentiment` | | ❌ | False | 显示情绪分析 |
| `--quiet` | `-q` | ❌ | False | 静默模式 |

## 📊 支持的Subreddit

脚本会从以下10个Reddit子版块搜索数据：

1. **stocks** - 股票讨论
2. **investing** - 投资分析
3. **wallstreetbets** - 散户投资者讨论
4. **SecurityAnalysis** - 证券分析
5. **ValueInvesting** - 价值投资
6. **StockMarket** - 股市讨论
7. **pennystocks** - 小盘股
8. **options** - 期权交易
9. **financialindependence** - 财务独立
10. **dividends** - 股息投资

## 🏷️ 支持的股票代码

脚本内置了34个常见股票代码的智能映射：

| 股票代码 | 搜索关键词 |
|----------|------------|
| AAPL | Apple, AAPL |
| TSLA | Tesla, TSLA |
| NVDA | Nvidia, NVDA |
| META | Meta, Facebook, META |
| GOOGL | Google, Alphabet, GOOGL |
| MSFT | Microsoft, MSFT |
| ... | ... |

对于未预设的股票代码，脚本会直接使用股票代码本身进行搜索。

## 📈 输出示例

### 控制台输出
```
📊 真实Reddit数据获取结果
============================================================
🏷️  股票代码: TSLA
📆 查找天数: 2
🔍 搜索关键词: Tesla, TSLA
📝 总帖子数: 8
⏱️  处理时间: 28.06秒
📅 时间范围: 2025-06-23 到 2025-06-25

📋 Subreddit分布:
   stocks: 3 个帖子
   ValueInvesting: 3 个帖子
   StockMarket: 1 个帖子
   options: 1 个帖子

📖 帖子详情 (显示前5个最热门的):
------------------------------------------------------------

1. 【stocks】Tesla's Robotaxi stunt backfires
   ⬆️  1352 分 | 💬 245 评论 | 👍 85% 赞成率
   📅 2025-06-24 12:57:24 | 👤 Fearless-Ad884
   📄 Looks like Tesla's attempt to pump hype using influencers is backfiring hard...
   🔗 https://reddit.com/r/stocks/comments/1lj2e5k/teslas_robotaxi_stunt_backfires/
```

### 情绪分析输出
```
📈 情绪分析摘要:
------------------------------
总帖子数: 8
正面帖子: 8
负面帖子: 0
中性帖子: 0
整体情绪: positive
平均分数: 247.6
总分数: 1981
```

## 📄 保存的文件格式

### JSON文件 (机器可读)
```json
{
  "success": true,
  "ticker": "TSLA",
  "days_back": 2,
  "search_terms": ["Tesla", "TSLA"],
  "posts": [
    {
      "id": "1lj2e5k",
      "title": "Tesla's Robotaxi stunt backfires",
      "content": "Looks like Tesla's attempt to pump hype...",
      "score": 1352,
      "num_comments": 245,
      "upvote_ratio": 0.85,
      "created_date": "2025-06-24 12:57:24",
      "author": "Fearless-Ad884",
      "subreddit": "stocks",
      "permalink": "https://reddit.com/r/stocks/comments/1lj2e5k/teslas_robotaxi_stunt_backfires/"
    }
  ],
  "total_posts": 8,
  "subreddit_stats": {
    "stocks": 3,
    "ValueInvesting": 3,
    "StockMarket": 1,
    "options": 1
  },
  "processing_time": 28.06
}
```

### 文本文件 (人类可读)
```
Reddit真实数据获取结果 - TSLA
==================================================

股票代码: TSLA
查找天数: 2
搜索关键词: Tesla, TSLA
总帖子数: 8
处理时间: 28.06秒

帖子详情:
----------------------------------------

1. 【stocks】Tesla's Robotaxi stunt backfires
   分数: 1352 | 评论: 245 | 赞成率: 85%
   时间: 2025-06-24 12:57:24 | 作者: Fearless-Ad884
   内容: Looks like Tesla's attempt to pump hype using influencers is backfiring hard...
   链接: https://reddit.com/r/stocks/comments/1lj2e5k/teslas_robotaxi_stunt_backfires/
```

## 🔧 技术特性

### API限制处理
- **智能延迟**: 在请求间添加延迟避免API限制
- **错误处理**: 优雅处理API错误和网络问题
- **重试机制**: 自动处理临时失败

### 数据质量保证
- **相关性检查**: 验证帖子内容确实包含搜索关键词
- **时间过滤**: 精确的时间范围控制
- **去重处理**: 自动去除重复帖子

### 性能优化
- **并发搜索**: 同时处理多个搜索关键词
- **智能限制**: 合理分配每个subreddit的帖子配额
- **内存优化**: 高效的数据结构和处理流程

## 🛠️ 环境要求

### Python包依赖
```bash
pip install praw  # Reddit API包装器
```

### Reddit API配置
脚本已配置好Reddit API凭证，可直接使用。如需使用自己的API凭证，请修改脚本中的配置：

```python
self.reddit = praw.Reddit(
    client_id="your_client_id",
    client_secret="your_client_secret", 
    user_agent="your_app_name by /u/your_username"
)
```

## 📊 实际测试结果

### AAPL测试 (3天，5个帖子/subreddit)
- ✅ 获取到11个真实帖子
- ✅ 覆盖5个subreddit
- ✅ 处理时间: 28.79秒
- ✅ 情绪分析: 正面情绪占主导

### TSLA测试 (2天，3个帖子/subreddit)
- ✅ 获取到8个真实帖子
- ✅ 覆盖4个subreddit
- ✅ 处理时间: 28.06秒
- ✅ 情绪分析: 100%正面情绪

## 🎯 使用建议

1. **合理设置天数**: 建议3-7天，太长可能获取过多数据
2. **适当限制数量**: 每个subreddit 5-15个帖子比较合适
3. **使用情绪分析**: 能够快速了解市场情绪倾向
4. **保存重要数据**: 对于重要分析建议保存原始数据
5. **注意API限制**: 避免过于频繁的请求

## 🔄 与模拟数据脚本的对比

| 特性 | test_reddit.py (真实数据) | reddit_data_fetcher.py (模拟数据) |
|------|---------------------------|-----------------------------------|
| 数据来源 | Reddit API (真实) | 本地JSONL文件 (模拟) |
| 数据新鲜度 | 实时 | 静态示例 |
| 网络需求 | 需要 | 不需要 |
| API密钥 | 需要 | 不需要 |
| 数据质量 | 真实市场讨论 | 预设示例内容 |
| 处理速度 | 较慢 (API调用) | 很快 (本地读取) |
| 适用场景 | 实际分析 | 开发测试 |

现在您有了一个完全功能的真实Reddit数据获取脚本！🎉
