"""
回测配置管理模块

提供回测实验的配置管理，包括日期范围、模型选择、代理配置等。
"""

import os
import json
import yaml
from datetime import datetime, date
from typing import Dict, List, Optional, Union, Any
from dataclasses import dataclass, asdict
from pathlib import Path


@dataclass
class ModelConfig:
    """模型配置类"""
    provider: str = "deepseek"  # 模型提供商: "deepseek", "openai"
    deep_think_model: str = "deepseek-reasoner"  # 深度思考模型
    quick_think_model: str = "deepseek-chat"    # 快速思考模型
    temperature: float = 0.1
    max_tokens: Optional[int] = None
    
    def validate(self) -> bool:
        """验证模型配置"""
        valid_providers = ["deepseek", "openai"]
        if self.provider not in valid_providers:
            raise ValueError(f"不支持的模型提供商: {self.provider}. 支持的提供商: {valid_providers}")
        
        if self.temperature < 0 or self.temperature > 2:
            raise ValueError("温度参数必须在0-2之间")
            
        return True


@dataclass 
class AgentConfig:
    """代理配置类"""
    selected_analysts: List[str] = None  # 选择的分析师
    max_debate_rounds: int = 1
    max_risk_discuss_rounds: int = 1
    enable_memory: bool = True
    
    def __post_init__(self):
        if self.selected_analysts is None:
            self.selected_analysts = ["market", "social", "news", "fundamentals"]
    
    def validate(self) -> bool:
        """验证代理配置"""
        valid_analysts = ["market", "social", "news", "fundamentals"]
        for analyst in self.selected_analysts:
            if analyst not in valid_analysts:
                raise ValueError(f"不支持的分析师类型: {analyst}. 支持的类型: {valid_analysts}")
        
        if self.max_debate_rounds < 1:
            raise ValueError("辩论轮数必须大于0")
            
        return True


@dataclass
class BacktestConfig:
    """回测配置主类"""
    # 基本信息
    experiment_name: str = "default_backtest"
    description: str = ""
    
    # 时间范围
    start_date: str = "2024-01-01"
    end_date: str = "2024-12-31"
    
    # 股票列表
    symbols: List[str] = None
    
    # 模型配置
    model_config: ModelConfig = None
    
    # 代理配置
    agent_config: AgentConfig = None
    
    # 数据配置
    online_tools: bool = False  # 是否使用在线数据
    data_cache_enabled: bool = True
    
    # 输出配置
    output_dir: str = "backtest_results"
    save_detailed_logs: bool = True
    save_trade_decisions: bool = True
    
    # 性能分析配置
    benchmark_symbol: str = "SPY"  # 基准指数
    risk_free_rate: float = 0.02   # 无风险利率
    
    def __post_init__(self):
        if self.symbols is None:
            self.symbols = ["AAPL", "MSFT", "GOOGL", "AMZN", "TSLA"]
        if self.model_config is None:
            self.model_config = ModelConfig()
        if self.agent_config is None:
            self.agent_config = AgentConfig()
    
    def validate(self) -> bool:
        """验证配置的有效性"""
        # 验证日期格式和范围
        try:
            start = datetime.strptime(self.start_date, "%Y-%m-%d")
            end = datetime.strptime(self.end_date, "%Y-%m-%d")
            
            if start >= end:
                raise ValueError("开始日期必须早于结束日期")
                
            # 检查日期范围是否合理（不超过10年）
            if (end - start).days > 3650:
                raise ValueError("回测时间范围不能超过10年")
                
        except ValueError as e:
            if "time data" in str(e):
                raise ValueError("日期格式必须为 YYYY-MM-DD")
            raise e
        
        # 验证股票代码
        if not self.symbols:
            raise ValueError("必须指定至少一个股票代码")
        
        # 验证子配置
        self.model_config.validate()
        self.agent_config.validate()
        
        # 验证输出目录
        if not self.output_dir:
            raise ValueError("必须指定输出目录")
            
        return True
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'BacktestConfig':
        """从字典创建配置"""
        # 处理嵌套的配置对象
        if 'model_config' in data and isinstance(data['model_config'], dict):
            data['model_config'] = ModelConfig(**data['model_config'])
        if 'agent_config' in data and isinstance(data['agent_config'], dict):
            data['agent_config'] = AgentConfig(**data['agent_config'])
        
        return cls(**data)
    
    def save_to_file(self, filepath: str) -> None:
        """保存配置到文件"""
        filepath = Path(filepath)
        filepath.parent.mkdir(parents=True, exist_ok=True)
        
        if filepath.suffix.lower() == '.json':
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(self.to_dict(), f, indent=2, ensure_ascii=False)
        elif filepath.suffix.lower() in ['.yml', '.yaml']:
            with open(filepath, 'w', encoding='utf-8') as f:
                yaml.dump(self.to_dict(), f, default_flow_style=False, allow_unicode=True)
        else:
            raise ValueError("支持的文件格式: .json, .yml, .yaml")
    
    @classmethod
    def load_from_file(cls, filepath: str) -> 'BacktestConfig':
        """从文件加载配置"""
        filepath = Path(filepath)
        
        if not filepath.exists():
            raise FileNotFoundError(f"配置文件不存在: {filepath}")
        
        if filepath.suffix.lower() == '.json':
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
        elif filepath.suffix.lower() in ['.yml', '.yaml']:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)
        else:
            raise ValueError("支持的文件格式: .json, .yml, .yaml")
        
        return cls.from_dict(data)


def create_default_config() -> BacktestConfig:
    """创建默认配置"""
    return BacktestConfig(
        experiment_name="default_deepseek_backtest",
        description="使用DeepSeek模型的默认回测实验",
        start_date="2024-01-01",
        end_date="2024-03-31",
        symbols=["AAPL", "MSFT", "GOOGL"],
        model_config=ModelConfig(
            provider="deepseek",
            deep_think_model="deepseek-reasoner",
            quick_think_model="deepseek-chat"
        ),
        agent_config=AgentConfig(
            selected_analysts=["market", "news", "fundamentals"],
            max_debate_rounds=1
        ),
        online_tools=False,
        output_dir="backtest_results/default"
    )


def create_sample_configs() -> Dict[str, BacktestConfig]:
    """创建示例配置"""
    configs = {}
    
    # 快速测试配置
    configs["quick_test"] = BacktestConfig(
        experiment_name="quick_test",
        description="快速测试配置 - 单股票，短时间",
        start_date="2024-01-01",
        end_date="2024-01-03",
        symbols=["AAPL"],
        agent_config=AgentConfig(selected_analysts=["market"]),
        online_tools=False
    )
    
    # 全面分析配置
    configs["comprehensive"] = BacktestConfig(
        experiment_name="comprehensive_analysis",
        description="全面分析配置 - 多股票，所有分析师",
        start_date="2024-01-01",
        end_date="2024-06-30",
        symbols=["AAPL", "MSFT", "GOOGL", "AMZN", "TSLA"],
        agent_config=AgentConfig(
            selected_analysts=["market", "social", "news", "fundamentals"],
            max_debate_rounds=2
        ),
        online_tools=True
    )
    
    # 对比测试配置
    configs["model_comparison"] = BacktestConfig(
        experiment_name="model_comparison",
        description="模型对比测试",
        start_date="2024-01-01", 
        end_date="2024-03-31",
        symbols=["AAPL", "MSFT"],
        model_config=ModelConfig(temperature=0.3),
        online_tools=False
    )
    
    return configs
