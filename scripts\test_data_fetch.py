#!/usr/bin/env python3
"""
数据获取测试脚本
用于测试在线数据获取工具是否正常工作

使用方法:
python scripts/test_data_fetch.py
"""

import os
import sys
import json
from pathlib import Path
from datetime import datetime, timedelta
import subprocess

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from dotenv import load_dotenv

# 加载环境变量
load_dotenv()


class DataFetchTester:
    """数据获取测试器"""
    
    def __init__(self):
        self.project_root = project_root
        self.test_dir = self.project_root / "test_data"
        self.test_dir.mkdir(exist_ok=True)
        
        print(f"🧪 测试目录: {self.test_dir}")
    
    def check_environment(self) -> bool:
        """检查环境配置"""
        print("\n🔍 检查环境配置...")
        
        # 检查API密钥
        api_keys = {
            'DEEPSEEK_API_KEY': '用于LLM分析',
            'FINNHUB_API_KEY': '用于金融数据'
        }
        
        missing_keys = []
        for key, desc in api_keys.items():
            if os.getenv(key):
                print(f"   ✅ {key}: 已配置")
            else:
                print(f"   ❌ {key}: 未配置 ({desc})")
                missing_keys.append(key)
        
        if missing_keys:
            print(f"\n⚠️  缺少 {len(missing_keys)} 个API密钥，某些功能可能无法正常工作")
            return False
        
        print("✅ 环境配置检查通过")
        return True
    
    def check_scripts(self) -> bool:
        """检查脚本文件"""
        print("\n📝 检查脚本文件...")
        
        scripts = [
            "scripts/fetch_online_data.py",
            "scripts/batch_fetch_data.py",
            "scripts/data_fetch_config.yaml"
        ]
        
        all_exist = True
        for script in scripts:
            script_path = self.project_root / script
            if script_path.exists():
                print(f"   ✅ {script}: 存在")
            else:
                print(f"   ❌ {script}: 不存在")
                all_exist = False
        
        if all_exist:
            print("✅ 脚本文件检查通过")
        else:
            print("❌ 部分脚本文件缺失")
        
        return all_exist
    
    def test_single_fetch(self) -> bool:
        """测试单次数据获取"""
        print("\n🚀 测试单次数据获取...")
        
        # 使用较短的时间范围和单个股票进行测试
        test_symbol = "AAPL"
        end_date = datetime.now().strftime("%Y-%m-%d")
        start_date = (datetime.now() - timedelta(days=7)).strftime("%Y-%m-%d")
        
        cmd = [
            sys.executable,
            str(self.project_root / "scripts" / "fetch_online_data.py"),
            "--symbols", test_symbol,
            "--start-date", start_date,
            "--end-date", end_date,
            "--data-dir", str(self.test_dir),
            "--analysts", "market",  # 只测试市场分析师
            "--delay", "2.0",
            "--verbose"
        ]
        
        print(f"📝 测试命令: {' '.join(cmd)}")
        print(f"📊 测试股票: {test_symbol}")
        print(f"📅 测试时间: {start_date} ~ {end_date}")
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                print("✅ 单次数据获取测试通过")
                print("📤 输出摘要:")
                # 只显示最后几行输出
                output_lines = result.stdout.strip().split('\n')
                for line in output_lines[-5:]:
                    if line.strip():
                        print(f"   {line}")
                return True
            else:
                print("❌ 单次数据获取测试失败")
                print("📤 错误输出:")
                print(result.stderr)
                return False
                
        except subprocess.TimeoutExpired:
            print("⏰ 单次数据获取测试超时")
            return False
        except Exception as e:
            print(f"❌ 测试执行异常: {e}")
            return False
    
    def test_config_file(self) -> bool:
        """测试配置文件"""
        print("\n📋 测试配置文件...")
        
        config_file = self.project_root / "scripts" / "data_fetch_config.yaml"
        
        if not config_file.exists():
            print("❌ 配置文件不存在")
            return False
        
        try:
            import yaml
            with open(config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 检查必需字段
            required_fields = ['symbols', 'date_ranges', 'analysts']
            for field in required_fields:
                if field not in config:
                    print(f"❌ 配置文件缺少字段: {field}")
                    return False
                print(f"   ✅ {field}: 已配置")
            
            print("✅ 配置文件测试通过")
            return True
            
        except Exception as e:
            print(f"❌ 配置文件解析失败: {e}")
            return False
    
    def check_data_output(self) -> bool:
        """检查数据输出"""
        print("\n📁 检查数据输出...")
        
        # 检查是否有生成的数据文件
        data_files = list(self.test_dir.rglob('*'))
        data_files = [f for f in data_files if f.is_file()]
        
        if not data_files:
            print("⚠️  未找到生成的数据文件")
            return False
        
        print(f"📊 找到 {len(data_files)} 个数据文件:")
        for file in data_files[:10]:  # 只显示前10个文件
            relative_path = file.relative_to(self.test_dir)
            file_size = file.stat().st_size
            print(f"   📄 {relative_path} ({file_size} bytes)")
        
        if len(data_files) > 10:
            print(f"   ... 还有 {len(data_files) - 10} 个文件")
        
        # 检查汇总文件
        summary_files = list(self.test_dir.glob('data_summary_*.json'))
        if summary_files:
            summary_file = summary_files[0]
            try:
                with open(summary_file, 'r', encoding='utf-8') as f:
                    summary = json.load(f)
                print(f"📋 汇总文件: {summary_file.name}")
                print(f"   获取时间: {summary.get('metadata', {}).get('fetch_time', 'N/A')}")
                print(f"   股票代码: {summary.get('metadata', {}).get('symbols', 'N/A')}")
            except Exception as e:
                print(f"⚠️  读取汇总文件失败: {e}")
        
        print("✅ 数据输出检查通过")
        return True
    
    def cleanup_test_data(self):
        """清理测试数据"""
        print("\n🧹 清理测试数据...")
        
        try:
            import shutil
            if self.test_dir.exists():
                shutil.rmtree(self.test_dir)
                print("✅ 测试数据已清理")
        except Exception as e:
            print(f"⚠️  清理测试数据失败: {e}")
    
    def run_full_test(self) -> bool:
        """运行完整测试"""
        print("🧪 开始数据获取工具测试")
        print("=" * 50)
        
        test_results = []
        
        # 1. 检查环境
        test_results.append(("环境配置", self.check_environment()))
        
        # 2. 检查脚本文件
        test_results.append(("脚本文件", self.check_scripts()))
        
        # 3. 测试配置文件
        test_results.append(("配置文件", self.test_config_file()))
        
        # 4. 测试单次数据获取 (只有在有API密钥时才执行)
        if os.getenv('DEEPSEEK_API_KEY') and os.getenv('FINNHUB_API_KEY'):
            test_results.append(("单次数据获取", self.test_single_fetch()))
            test_results.append(("数据输出检查", self.check_data_output()))
        else:
            print("\n⚠️  跳过在线测试 (缺少API密钥)")
            test_results.append(("单次数据获取", None))
            test_results.append(("数据输出检查", None))
        
        # 打印测试结果
        print("\n" + "=" * 50)
        print("📊 测试结果汇总:")
        
        passed = 0
        failed = 0
        skipped = 0
        
        for test_name, result in test_results:
            if result is True:
                print(f"   ✅ {test_name}: 通过")
                passed += 1
            elif result is False:
                print(f"   ❌ {test_name}: 失败")
                failed += 1
            else:
                print(f"   ⏭️  {test_name}: 跳过")
                skipped += 1
        
        print(f"\n📈 统计: {passed} 通过, {failed} 失败, {skipped} 跳过")
        
        # 清理测试数据
        if input("\n🧹 是否清理测试数据? (y/N): ").lower() == 'y':
            self.cleanup_test_data()
        
        return failed == 0


def main():
    """主函数"""
    print("🧪 TradingAgents 数据获取工具测试")
    print("本测试将验证数据获取工具是否正常工作")
    print()
    
    tester = DataFetchTester()
    
    try:
        success = tester.run_full_test()
        
        if success:
            print("\n🎉 所有测试通过! 数据获取工具可以正常使用")
        else:
            print("\n⚠️  部分测试失败，请检查配置和环境")
            
    except KeyboardInterrupt:
        print("\n⏹️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试执行异常: {e}")


if __name__ == '__main__':
    main()
