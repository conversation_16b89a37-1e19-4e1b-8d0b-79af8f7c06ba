#!/usr/bin/env python3
"""
测试单个日期的Reddit数据获取
验证修复后的批量获取器是否能正常工作
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from reddit_batch_fetcher import RedditBatchFetcher
from datetime import datetime
import json


def test_single_date():
    """测试单个日期的数据获取"""
    print("🧪 测试单个日期的Reddit数据获取")
    print("=" * 50)
    
    try:
        # 创建获取器实例
        fetcher = RedditBatchFetcher()
        
        # 修改配置为测试模式
        fetcher.min_posts_per_day = 5  # 降低要求便于测试
        
        # 测试最近的日期
        test_date = datetime(2025, 6, 24)  # 昨天
        ticker = 'AAPL'
        
        print(f"📅 测试日期: {test_date.strftime('%Y-%m-%d')}")
        print(f"📊 测试股票: {ticker}")
        print(f"🎯 最小帖子数: {fetcher.min_posts_per_day}")
        
        # 创建测试文件夹
        fetcher.create_folder_structure()
        
        # 获取单日数据
        posts = fetcher.fetch_posts_for_date(ticker, test_date)
        
        print(f"\n📊 获取结果:")
        print(f"   找到帖子数: {len(posts)}")
        
        if posts:
            print(f"   示例帖子:")
            for i, post in enumerate(posts[:3], 1):
                print(f"     {i}. [{post['subreddit']}] {post['title'][:60]}...")
                print(f"        得分: {post['score']} | 评论: {post['num_comments']} | 情绪: {post['sentiment']['label']}")
        
        # 保存数据
        if posts:
            save_data = fetcher.save_daily_posts(ticker, test_date, posts)
            print(f"\n💾 数据已保存到: {fetcher.target_stocks[ticker]['folder']}")
            
            # 显示统计信息
            stats = save_data['statistics']
            print(f"\n📈 统计信息:")
            print(f"   总帖子数: {save_data['total_posts']}")
            print(f"   平均情绪得分: {stats['average_sentiment_score']}")
            print(f"   情绪分布: {stats['sentiment_distribution']}")
            print(f"   subreddit分布: {stats['subreddit_distribution']}")
            
            if save_data['metadata']['target_met']:
                print("   ✅ 达到目标帖子数")
            else:
                print("   ⚠️ 未达到目标帖子数")
        else:
            print("   ❌ 没有找到相关帖子")
        
        print(f"\n✅ 单日测试完成!")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_single_date()
