#!/usr/bin/env python3
"""
TradingAgents 回测运行器

主要的回测脚本，提供命令行界面和完整的回测流程。

使用方法:
    python backtest_runner.py --config config.json
    python backtest_runner.py --quick-test
    python backtest_runner.py --list-experiments
"""

import argparse
import sys
import os
from pathlib import Path
import pandas as pd
from datetime import datetime

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from tradingagents.backtesting import (
    BacktestConfig, BacktestEngine, PerformanceAnalyzer, 
    ResultVisualizer, ExperimentManager
)
from tradingagents.backtesting.config import create_default_config, create_sample_configs
from tradingagents.default_config import DEFAULT_CONFIG


class BacktestRunner:
    """回测运行器主类"""
    
    def __init__(self):
        """初始化回测运行器"""
        self.experiment_manager = ExperimentManager()
        self.engine = BacktestEngine(self.experiment_manager)
        self.performance_analyzer = PerformanceAnalyzer()
        self.visualizer = ResultVisualizer()
        
    def run_backtest(self, config: BacktestConfig, generate_reports: bool = True) -> str:
        """
        运行回测
        
        Args:
            config: 回测配置
            generate_reports: 是否生成报告
            
        Returns:
            实验ID
        """
        print("🚀 开始回测...")
        print(f"实验名称: {config.experiment_name}")
        print(f"时间范围: {config.start_date} ~ {config.end_date}")
        print(f"股票列表: {', '.join(config.symbols)}")
        print(f"模型提供商: {config.model_config.provider}")
        print(f"分析师: {', '.join(config.agent_config.selected_analysts)}")
        print("-" * 60)
        
        # 检查和设置数据
        if not self.check_and_setup_data(config):
            raise ValueError("数据准备失败，无法继续回测")
        
        # 执行回测
        backtest_result = self.engine.run_backtest(config)
        experiment_id = backtest_result["experiment_id"]
        
        if not generate_reports:
            return experiment_id
        
        # 生成性能分析和可视化报告
        self.generate_comprehensive_reports(experiment_id, backtest_result)
        
        return experiment_id
    
    def generate_comprehensive_reports(self, experiment_id: str, backtest_result: dict) -> None:
        """
        生成综合报告
        
        Args:
            experiment_id: 实验ID
            backtest_result: 回测结果
        """
        print("\n📊 生成分析报告...")
        
        exp_dir = self.experiment_manager.get_experiment_directory(experiment_id)
        results = backtest_result["results"]
        config = backtest_result["config"]
        
        # 转换为DataFrame
        trades_df = pd.DataFrame([
            {
                "symbol": r["symbol"],
                "date": r["date"], 
                "decision": r["decision"],
                "success": r["success"]
            }
            for r in results if r["success"]
        ])
        
        if trades_df.empty:
            print("⚠️  没有成功的交易记录，跳过性能分析")
            return
        
        # 加载价格数据
        price_data = self.load_price_data(config.symbols)
        
        # 计算收益
        returns_df = self.performance_analyzer.calculate_returns(trades_df, price_data)
        
        if returns_df.empty:
            print("⚠️  无法计算收益，跳过性能分析")
            return
        
        # 加载基准数据
        benchmark_data = None
        if config.benchmark_symbol:
            benchmark_data = price_data.get(config.benchmark_symbol)
        
        # 生成性能报告
        performance_report = self.performance_analyzer.generate_performance_report(
            returns_df, benchmark_data
        )
        
        # 保存性能报告
        report_file = exp_dir / "reports" / "performance_report.json"
        import json
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(performance_report, f, indent=2, ensure_ascii=False, default=str)
        
        # 生成可视化报告
        charts_dir = exp_dir / "charts"
        self.visualizer.generate_comprehensive_report(
            returns_df, performance_report, benchmark_data, str(charts_dir)
        )
        
        # 保存性能指标表格
        self.visualizer.save_performance_table(
            performance_report, 
            str(exp_dir / "reports" / "performance_metrics.csv")
        )
        
        # 打印性能摘要
        self.performance_analyzer.print_performance_summary(performance_report)
        
        print(f"\n✅ 报告已生成: {exp_dir}")
        print(f"   - 性能报告: {report_file}")
        print(f"   - 图表目录: {charts_dir}")
        print(f"   - 性能指标: {exp_dir / 'reports' / 'performance_metrics.csv'}")
    
    def load_price_data(self, symbols: list) -> dict:
        """
        加载价格数据
        
        Args:
            symbols: 股票代码列表
            
        Returns:
            价格数据字典
        """
        price_data = {}
        data_dir = Path(DEFAULT_CONFIG.get("data_dir", "data"))
        
        for symbol in symbols:
            data_file = data_dir / "market_data" / "price_data" / f"{symbol}-YFin-data-2015-01-01-2025-03-25.csv"
            
            if data_file.exists():
                try:
                    df = pd.read_csv(data_file)
                    price_data[symbol] = df
                except Exception as e:
                    print(f"⚠️  加载 {symbol} 价格数据失败: {e}")
        
        return price_data
    
    def list_experiments(self) -> None:
        """列出所有实验"""
        self.experiment_manager.print_experiment_summary()
    
    def create_sample_configs(self, output_dir: str = "configs") -> None:
        """
        创建示例配置文件
        
        Args:
            output_dir: 输出目录
        """
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 创建默认配置
        default_config = create_default_config()
        default_config.save_to_file(str(output_path / "default_config.json"))
        
        # 创建示例配置
        sample_configs = create_sample_configs()
        for name, config in sample_configs.items():
            config.save_to_file(str(output_path / f"{name}_config.json"))
        
        print(f"✅ 示例配置已创建: {output_path}")
        for config_file in output_path.glob("*.json"):
            print(f"   - {config_file.name}")
    
    def check_and_setup_data(self, config: BacktestConfig) -> bool:
        """
        检查数据可用性，如果缺失则尝试自动下载
        
        Args:
            config: 回测配置
            
        Returns:
            是否数据准备就绪
        """
        print("🔍 检查数据可用性...")
        
        # 检查数据目录
        data_dir = Path(DEFAULT_CONFIG.get("data_dir", "data"))
        if not data_dir.exists():
            print(f"📁 数据目录不存在，正在创建: {data_dir}")
            data_dir.mkdir(parents=True, exist_ok=True)
            (data_dir / "market_data" / "price_data").mkdir(parents=True, exist_ok=True)
        
        # 检查股票数据文件
        missing_symbols = []
        price_data_dir = data_dir / "market_data" / "price_data"
        
        for symbol in config.symbols:
            data_file = price_data_dir / f"{symbol}-YFin-data-2015-01-01-2025-03-25.csv"
            if not data_file.exists():
                # 查找任何该股票的数据文件
                pattern = f"{symbol}-YFin-data-*.csv"
                existing_files = list(price_data_dir.glob(pattern))
                if not existing_files:
                    missing_symbols.append(symbol)
        
        if missing_symbols:
            print(f"⚠️  缺失数据的股票: {', '.join(missing_symbols)}")
            choice = input("是否自动下载缺失的股票数据? (y/n): ").strip().lower()
            
            if choice in ['y', 'yes']:
                return self.download_missing_data(missing_symbols)
            else:
                print("❌ 用户选择不下载数据，回测终止")
                return False
        
        print("✅ 所有股票数据已准备就绪")
        return True
    
    def download_missing_data(self, symbols: list) -> bool:
        """
        下载缺失的股票数据
        
        Args:
            symbols: 缺失数据的股票代码列表
            
        Returns:
            是否下载成功
        """
        print(f"📥 正在下载缺失的股票数据: {', '.join(symbols)}")
        
        try:
            import yfinance as yf
            from datetime import datetime
            
            data_dir = Path(DEFAULT_CONFIG.get("data_dir", "data"))
            price_data_dir = data_dir / "market_data" / "price_data"
            
            for symbol in symbols:
                try:
                    print(f"📊 下载 {symbol} 数据...")
                    
                    # 下载数据
                    ticker = yf.Ticker(symbol)
                    data = ticker.history(start="2015-01-01", end="2025-03-25")
                    
                    if data.empty:
                        print(f"⚠️  {symbol}: 无数据可下载")
                        continue
                    
                    # 重置索引，使Date成为一列
                    data.reset_index(inplace=True)
                    
                    # 保存数据
                    filename = f"{symbol}-YFin-data-2015-01-01-2025-03-25.csv"
                    filepath = price_data_dir / filename
                    data.to_csv(filepath, index=False)
                    
                    print(f"✅ {symbol}: {len(data)} 条记录已保存")
                    
                except Exception as e:
                    print(f"❌ {symbol}: 下载失败 - {e}")
                    return False
            
            return True
            
        except ImportError:
            print("❌ 缺少yfinance库，请安装: pip install yfinance")
            return False
        except Exception as e:
            print(f"❌ 下载数据时出错: {e}")
            return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="TradingAgents 回测框架",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  # 使用配置文件运行回测
  python backtest_runner.py --config configs/default_config.json
  
  # 快速测试
  python backtest_runner.py --quick-test
  
  # 列出所有实验
  python backtest_runner.py --list-experiments
  
  # 创建示例配置
  python backtest_runner.py --create-configs
        """
    )
    
    parser.add_argument(
        "--config", "-c",
        type=str,
        help="配置文件路径 (JSON/YAML)"
    )
    
    parser.add_argument(
        "--quick-test", "-q",
        action="store_true",
        help="运行快速测试（单股票，短时间）"
    )
    
    parser.add_argument(
        "--list-experiments", "-l",
        action="store_true", 
        help="列出所有实验"
    )
    
    parser.add_argument(
        "--create-configs",
        action="store_true",
        help="创建示例配置文件"
    )
    
    parser.add_argument(
        "--experiment-id", "-e",
        type=str,
        help="指定实验ID（用于重新生成报告）"
    )
    
    parser.add_argument(
        "--no-reports",
        action="store_true",
        help="不生成分析报告（仅执行回测）"
    )
    
    args = parser.parse_args()
    
    # 创建回测运行器
    runner = BacktestRunner()
    
    try:
        if args.list_experiments:
            runner.list_experiments()
            
        elif args.create_configs:
            runner.create_sample_configs()
            
        elif args.quick_test:
            print("🏃‍♂️ 运行快速测试...")
            config = create_sample_configs()["quick_test"]
            experiment_id = runner.run_backtest(config, not args.no_reports)
            print(f"\n✅ 快速测试完成: {experiment_id}")
            
        elif args.config:
            config_path = Path(args.config)
            if not config_path.exists():
                print(f"❌ 配置文件不存在: {config_path}")
                sys.exit(1)
            
            print(f"📖 加载配置: {config_path}")
            config = BacktestConfig.load_from_file(str(config_path))
            
            # 检查和准备数据
            if not runner.check_and_setup_data(config):
                print("❌ 数据准备失败，回测终止")
                sys.exit(1)
            
            experiment_id = runner.run_backtest(config, not args.no_reports)
            print(f"\n✅ 回测完成: {experiment_id}")
            
        elif args.experiment_id:
            # 重新生成指定实验的报告
            experiment = runner.experiment_manager.get_experiment(args.experiment_id)
            if not experiment:
                print(f"❌ 实验不存在: {args.experiment_id}")
                sys.exit(1)
            
            print(f"📊 重新生成实验报告: {args.experiment_id}")
            # 这里需要重新加载实验结果并生成报告
            # 简化实现，实际应该从保存的结果中重新生成
            print("⚠️  重新生成报告功能待实现")
            
        else:
            print("❓ 请指定操作参数，使用 --help 查看帮助")
            parser.print_help()
            
    except KeyboardInterrupt:
        print("\n⏹️  用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 执行失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
