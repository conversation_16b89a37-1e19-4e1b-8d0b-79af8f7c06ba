#!/usr/bin/env python3
"""
社交媒体分析师数据获取脚本

这个脚本独立实现了社交媒体分析师的数据获取功能，包括：
1. 在线模式：通过DeepSeek/OpenAI API搜索社交媒体内容
2. 离线模式：从Reddit数据获取股票相关讨论
3. 数据质量验证和格式化输出
4. 详细的获取过程展示

使用方法：
python scripts/social_media_data_fetcher.py --ticker AAPL --date 2024-05-20 --mode online
python scripts/social_media_data_fetcher.py --ticker TSLA --date 2024-05-20 --mode offline
"""

import os
import sys
import json
import argparse
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional
from dotenv import load_dotenv

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 导入项目模块
from tradingagents.dataflows.interface import get_llm_client, get_reddit_company_news
from tradingagents.dataflows.reddit_utils import fetch_top_from_category
from tradingagents.dataflows.config import DATA_DIR

# 加载环境变量
load_dotenv()


class SocialMediaDataFetcher:
    """社交媒体数据获取器"""
    
    def __init__(self, debug: bool = True):
        """
        初始化数据获取器
        
        Args:
            debug: 是否显示详细调试信息
        """
        self.debug = debug
        self.data_dir = Path(DATA_DIR)
        
        # 检查环境配置
        self._check_environment()
        
        # 初始化LLM客户端（用于在线模式）
        try:
            self.llm_client = get_llm_client()
            self.online_available = True
            if self.debug:
                print("✅ LLM客户端初始化成功")
        except Exception as e:
            self.llm_client = None
            self.online_available = False
            if self.debug:
                print(f"⚠️ LLM客户端初始化失败: {e}")
    
    def _check_environment(self):
        """检查环境配置"""
        if self.debug:
            print("🔍 检查环境配置...")
        
        # 检查API密钥
        deepseek_key = os.getenv('DEEPSEEK_API_KEY')
        openai_key = os.getenv('OPENAI_API_KEY')
        
        if deepseek_key:
            print("✅ DeepSeek API密钥已配置")
        elif openai_key:
            print("✅ OpenAI API密钥已配置")
        else:
            print("⚠️ 未找到API密钥，在线模式将不可用")
        
        # 检查Reddit数据目录
        reddit_data_dir = self.data_dir / "reddit_data"
        if reddit_data_dir.exists():
            print(f"✅ Reddit数据目录存在: {reddit_data_dir}")
        else:
            print(f"⚠️ Reddit数据目录不存在: {reddit_data_dir}")
    
    def fetch_online_data(self, ticker: str, date: str) -> Dict[str, Any]:
        """
        在线模式：通过LLM API获取社交媒体数据
        
        Args:
            ticker: 股票代码
            date: 目标日期 (YYYY-MM-DD)
            
        Returns:
            包含获取结果的字典
        """
        if not self.online_available:
            return {
                'success': False,
                'error': 'LLM客户端不可用，请检查API密钥配置',
                'data': None
            }
        
        if self.debug:
            print(f"\n📱 在线模式：获取 {ticker} 在 {date} 的社交媒体数据")
            print("=" * 50)
        
        try:
            # 确定使用的模型
            model = "deepseek-chat" if "deepseek" in str(self.llm_client.base_url) else "gpt-4o-mini"
            
            if self.debug:
                print(f"🤖 使用模型: {model}")
                print(f"🌐 API端点: {self.llm_client.base_url}")
            
            # 构建查询提示
            prompt = f"""请搜索关于股票 {ticker} 在 {date} 前7天内的社交媒体内容。

请重点关注以下平台的内容：
- Twitter/X 上的讨论
- Reddit 股票相关板块的帖子
- 财经社交媒体平台的讨论
- 投资者论坛的观点

请只返回在 {date} 前7天内发布的内容，包括：
1. 投资者情绪和观点
2. 重要新闻的社交媒体反应
3. 技术分析讨论
4. 基本面分析讨论
5. 市场传言和预期

请提供详细的分析，包括正面、负面和中性观点的平衡。"""

            if self.debug:
                print("📤 发送API请求...")
                print(f"📝 查询内容: {prompt[:100]}...")
            
            # 发送API请求
            start_time = time.time()
            response = self.llm_client.chat.completions.create(
                model=model,
                messages=[
                    {
                        "role": "system",
                        "content": f"你是一个专业的社交媒体分析师，专门分析股票相关的社交媒体内容。请确保只获取 {date} 前7天内发布的数据。"
                    },
                    {
                        "role": "user", 
                        "content": prompt
                    }
                ],
                temperature=0.1,
                max_tokens=4096,
            )
            
            end_time = time.time()
            
            # 提取响应内容
            content = response.choices[0].message.content
            
            if self.debug:
                print(f"📥 API响应成功 (耗时: {end_time - start_time:.2f}秒)")
                print(f"📄 内容长度: {len(content)} 字符")
                print(f"📊 使用的tokens: {getattr(response.usage, 'total_tokens', 'N/A')}")
            
            # 验证内容质量
            quality_score = self._assess_content_quality(content)
            
            return {
                'success': True,
                'method': 'online_api',
                'model': model,
                'ticker': ticker,
                'date': date,
                'content': content,
                'content_length': len(content),
                'quality_score': quality_score,
                'api_response_time': end_time - start_time,
                'tokens_used': getattr(response.usage, 'total_tokens', None),
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            error_msg = str(e)
            if self.debug:
                print(f"❌ API请求失败: {error_msg}")
            
            return {
                'success': False,
                'error': error_msg,
                'method': 'online_api',
                'ticker': ticker,
                'date': date,
                'timestamp': datetime.now().isoformat()
            }
    
    def fetch_offline_data(self, ticker: str, date: str) -> Dict[str, Any]:
        """
        离线模式：从Reddit数据获取股票相关讨论
        
        Args:
            ticker: 股票代码
            date: 目标日期 (YYYY-MM-DD)
            
        Returns:
            包含获取结果的字典
        """
        if self.debug:
            print(f"\n💾 离线模式：获取 {ticker} 在 {date} 的Reddit数据")
            print("=" * 50)
        
        try:
            # 检查Reddit数据目录
            reddit_data_dir = self.data_dir / "reddit_data" / "company_news"
            
            if not reddit_data_dir.exists():
                return {
                    'success': False,
                    'error': f'Reddit数据目录不存在: {reddit_data_dir}',
                    'method': 'offline_reddit',
                    'ticker': ticker,
                    'date': date
                }
            
            if self.debug:
                print(f"📂 数据目录: {reddit_data_dir}")
                
                # 显示可用的subreddit
                subreddits = [d.name for d in reddit_data_dir.iterdir() if d.is_dir()]
                print(f"📋 可用的subreddit: {subreddits}")
            
            # 使用接口函数获取Reddit公司新闻
            start_time = time.time()
            
            if self.debug:
                print(f"🔍 搜索 {ticker} 相关讨论...")
                print(f"📅 目标日期: {date}")
                print(f"📆 回溯天数: 7天")
                print(f"📊 每日最大条数: 5条")
            
            # 调用Reddit数据获取函数
            reddit_content = get_reddit_company_news(
                ticker=ticker,
                start_date=date,
                look_back_days=7,
                max_limit_per_day=5
            )
            
            end_time = time.time()
            
            if self.debug:
                print(f"⏱️ 数据获取完成 (耗时: {end_time - start_time:.2f}秒)")
            
            # 检查是否获取到数据
            if not reddit_content or reddit_content.strip() == "":
                if self.debug:
                    print("⚠️ 未找到相关Reddit讨论数据")
                
                return {
                    'success': True,
                    'method': 'offline_reddit',
                    'ticker': ticker,
                    'date': date,
                    'content': '',
                    'content_length': 0,
                    'posts_found': 0,
                    'message': '未找到相关讨论数据',
                    'processing_time': end_time - start_time,
                    'timestamp': datetime.now().isoformat()
                }
            
            # 分析内容
            posts_count = reddit_content.count('###')  # 每个帖子标题前有###
            quality_score = self._assess_content_quality(reddit_content)
            
            if self.debug:
                print(f"📄 内容长度: {len(reddit_content)} 字符")
                print(f"📝 找到帖子: {posts_count} 条")
                print(f"⭐ 质量评分: {quality_score}/10")
            
            return {
                'success': True,
                'method': 'offline_reddit',
                'ticker': ticker,
                'date': date,
                'content': reddit_content,
                'content_length': len(reddit_content),
                'posts_found': posts_count,
                'quality_score': quality_score,
                'processing_time': end_time - start_time,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            error_msg = str(e)
            if self.debug:
                print(f"❌ 离线数据获取失败: {error_msg}")
            
            return {
                'success': False,
                'error': error_msg,
                'method': 'offline_reddit',
                'ticker': ticker,
                'date': date,
                'timestamp': datetime.now().isoformat()
            }
    
    def _assess_content_quality(self, content: str) -> float:
        """
        评估内容质量
        
        Args:
            content: 要评估的内容
            
        Returns:
            质量评分 (0-10)
        """
        if not content or len(content.strip()) == 0:
            return 0.0
        
        score = 0.0
        
        # 基础长度评分 (0-3分)
        if len(content) > 100:
            score += 1.0
        if len(content) > 500:
            score += 1.0
        if len(content) > 1000:
            score += 1.0
        
        # 内容多样性评分 (0-3分)
        financial_keywords = ['股票', '投资', '交易', '分析', '价格', 'stock', 'investment', 'trading', 'analysis', 'price']
        sentiment_keywords = ['看涨', '看跌', '乐观', '悲观', 'bullish', 'bearish', 'positive', 'negative']
        social_keywords = ['讨论', '观点', '评论', '社区', 'discussion', 'opinion', 'comment', 'community']
        
        for keywords in [financial_keywords, sentiment_keywords, social_keywords]:
            if any(keyword.lower() in content.lower() for keyword in keywords):
                score += 1.0
        
        # 结构化内容评分 (0-2分)
        if '###' in content or '#' in content:  # 有标题结构
            score += 1.0
        if len(content.split('\n')) > 5:  # 有多行内容
            score += 1.0
        
        # 避免指导性文本 (0-2分)
        instructional_phrases = [
            'I cannot search', 'I don\'t have access', 'I cannot browse',
            '我无法搜索', '我没有访问权限', '我无法浏览'
        ]
        
        if not any(phrase.lower() in content.lower() for phrase in instructional_phrases):
            score += 2.0
        
        return min(score, 10.0)

    def save_results(self, results: Dict[str, Any], output_dir: Optional[Path] = None) -> Path:
        """
        保存获取结果到文件

        Args:
            results: 获取结果
            output_dir: 输出目录，默认为data/social_media_analysis

        Returns:
            保存的文件路径
        """
        if output_dir is None:
            output_dir = self.data_dir / "social_media_analysis"

        output_dir.mkdir(parents=True, exist_ok=True)

        # 生成文件名
        ticker = results.get('ticker', 'unknown')
        date = results.get('date', 'unknown')
        method = results.get('method', 'unknown')
        timestamp = datetime.now().strftime("%H%M%S")

        filename = f"{ticker}-{method}-{date}-{timestamp}.json"
        filepath = output_dir / filename

        # 保存JSON文件
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)

        # 如果有内容，也保存纯文本文件
        if results.get('content'):
            txt_filename = f"{ticker}-{method}-{date}-{timestamp}.txt"
            txt_filepath = output_dir / txt_filename

            with open(txt_filepath, 'w', encoding='utf-8') as f:
                f.write(results['content'])

        if self.debug:
            print(f"💾 结果已保存到: {filepath}")

        return filepath

    def display_results(self, results: Dict[str, Any]):
        """
        显示获取结果

        Args:
            results: 获取结果
        """
        print("\n" + "=" * 60)
        print("📊 社交媒体数据获取结果")
        print("=" * 60)

        # 基本信息
        print(f"🏷️  股票代码: {results.get('ticker', 'N/A')}")
        print(f"📅 目标日期: {results.get('date', 'N/A')}")
        print(f"🔧 获取方式: {results.get('method', 'N/A')}")
        print(f"✅ 获取状态: {'成功' if results.get('success') else '失败'}")

        if not results.get('success'):
            print(f"❌ 错误信息: {results.get('error', 'N/A')}")
            return

        # 详细信息
        if 'content_length' in results:
            print(f"📄 内容长度: {results['content_length']} 字符")

        if 'posts_found' in results:
            print(f"📝 找到帖子: {results['posts_found']} 条")

        if 'quality_score' in results:
            print(f"⭐ 质量评分: {results['quality_score']:.1f}/10")

        if 'api_response_time' in results:
            print(f"⏱️  API响应时间: {results['api_response_time']:.2f}秒")

        if 'processing_time' in results:
            print(f"⏱️  处理时间: {results['processing_time']:.2f}秒")

        if 'tokens_used' in results and results['tokens_used']:
            print(f"🎯 使用Tokens: {results['tokens_used']}")

        if 'model' in results:
            print(f"🤖 使用模型: {results['model']}")

        # 内容预览
        content = results.get('content', '')
        if content:
            print(f"\n📖 内容预览 (前500字符):")
            print("-" * 40)
            print(content[:500])
            if len(content) > 500:
                print("...")
        else:
            print("\n📖 内容: 无")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="社交媒体分析师数据获取脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 在线模式获取AAPL数据
  python scripts/social_media_data_fetcher.py --ticker AAPL --date 2024-05-20 --mode online

  # 离线模式获取TSLA数据
  python scripts/social_media_data_fetcher.py --ticker TSLA --date 2024-05-20 --mode offline

  # 同时使用两种模式
  python scripts/social_media_data_fetcher.py --ticker NVDA --date 2024-05-20 --mode both
        """
    )

    parser.add_argument(
        '--ticker', '-t',
        required=True,
        help='股票代码 (例如: AAPL, TSLA, NVDA)'
    )

    parser.add_argument(
        '--date', '-d',
        required=True,
        help='目标日期，格式: YYYY-MM-DD (例如: 2024-05-20)'
    )

    parser.add_argument(
        '--mode', '-m',
        choices=['online', 'offline', 'both'],
        default='both',
        help='获取模式: online(在线API), offline(本地Reddit), both(两种都用)'
    )

    parser.add_argument(
        '--output', '-o',
        type=Path,
        help='输出目录 (默认: data/social_media_analysis)'
    )

    parser.add_argument(
        '--quiet', '-q',
        action='store_true',
        help='静默模式，减少输出信息'
    )

    parser.add_argument(
        '--save',
        action='store_true',
        help='保存结果到文件'
    )

    args = parser.parse_args()

    # 验证日期格式
    try:
        datetime.strptime(args.date, '%Y-%m-%d')
    except ValueError:
        print("❌ 日期格式错误，请使用 YYYY-MM-DD 格式")
        sys.exit(1)

    # 初始化数据获取器
    fetcher = SocialMediaDataFetcher(debug=not args.quiet)

    print("🚀 社交媒体数据获取脚本")
    print("=" * 50)
    print(f"📊 股票代码: {args.ticker}")
    print(f"📅 目标日期: {args.date}")
    print(f"🔧 获取模式: {args.mode}")

    results = []

    # 在线模式
    if args.mode in ['online', 'both']:
        print(f"\n🌐 开始在线数据获取...")
        online_result = fetcher.fetch_online_data(args.ticker, args.date)
        results.append(online_result)

        if not args.quiet:
            fetcher.display_results(online_result)

        if args.save:
            fetcher.save_results(online_result, args.output)

    # 离线模式
    if args.mode in ['offline', 'both']:
        print(f"\n💾 开始离线数据获取...")
        offline_result = fetcher.fetch_offline_data(args.ticker, args.date)
        results.append(offline_result)

        if not args.quiet:
            fetcher.display_results(offline_result)

        if args.save:
            fetcher.save_results(offline_result, args.output)

    # 总结
    print(f"\n🎯 获取完成!")
    successful = sum(1 for r in results if r.get('success'))
    print(f"✅ 成功: {successful}/{len(results)}")

    if args.mode == 'both' and len(results) == 2:
        print(f"\n📊 模式对比:")
        for result in results:
            method = result.get('method', 'unknown')
            success = '✅' if result.get('success') else '❌'
            length = result.get('content_length', 0)
            print(f"  {method}: {success} ({length} 字符)")


if __name__ == "__main__":
    main()
